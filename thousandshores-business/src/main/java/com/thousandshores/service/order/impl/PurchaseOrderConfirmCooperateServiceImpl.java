package com.thousandshores.service.order.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipOutputStream;

import com.alibaba.dubbo.config.annotation.Reference;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import com.shores.file.service.DubboFileService;
import com.thousandshores.common.constant.AppConstant;
import com.thousandshores.common.core.domain.model.LoginUser;
import com.thousandshores.common.enums.FileSourceType;
import com.thousandshores.common.enums.file.FileBusinessEnum;
import com.thousandshores.enums.order.SkuBarCodeType;
import com.thousandshores.system.domain.erpentity.PurchaseWarehouseContact;
import com.thousandshores.system.enums.erp.PriceType3;
import com.thousandshores.enums.order.PurchaseOrderConfirmCooperateDataStatus;
import com.thousandshores.system.enums.erp.WarehouseBarcodeEnum;
import com.thousandshores.common.exception.ServiceException;
import com.thousandshores.common.utils.*;
import com.thousandshores.common.utils.file.FileUtil;
import com.thousandshores.domain.order.*;
import com.thousandshores.file.domain.OssUploadFileTfg;
import com.thousandshores.file.service.OssUploadFileService;
import com.thousandshores.file.service.OssUploadFileTfgService;
import com.thousandshores.mapper.order.PurchaseOrderConfirmCooperateMapper;
import com.thousandshores.service.order.IPurchaseOrderConfirmCooperateService;
import com.thousandshores.service.order.PurchaseBatchCodeService;
import com.thousandshores.system.domain.erpentity.PurchaseOrder;
import com.thousandshores.system.domain.erpentity.PurchaseOrderDetail;
import com.thousandshores.system.domain.erpentity.VendorMaster;
import com.thousandshores.system.service.ISysConfigService;
import com.thousandshores.system.service.erp.IVendorMasterService;
import com.thousandshores.system.service.erp.PurchaseOrderService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.xhtmlrenderer.layout.SharedContext;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 采购订单确认Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class PurchaseOrderConfirmCooperateServiceImpl implements IPurchaseOrderConfirmCooperateService
{
    private static final String FONT_PATH = "ttf"+File.separator+"NotoSansHans-Medium.ttf";

    private final Logger logs = LoggerFactory.getLogger( this.getClass() );

    @Autowired
    private PurchaseOrderConfirmCooperateMapper purchaseOrderConfirmCooperateMapper;

    @Autowired
    private OssUploadFileService ossUploadFileService;

    @Autowired
    private IVendorMasterService vendorMasterService;

    @Reference
    private DubboFileService dubboFileService;

    @Autowired
    private OssUploadFileTfgService ossUploadFileTfgService;

    @Autowired
    private PurchaseBatchCodeService purchaseBatchCodeService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private PurchaseOrderService purchaseOrderService;

    /**
     * 查询采购订单确认
     * 
     * @param id 采购订单确认主键
     * @return 采购订单确认
     */
    @Override
    public PurchaseOrderConfirmCooperate selectPurchaseOrderConfirmCooperateById(Long id)
    {
        return purchaseOrderConfirmCooperateMapper.selectPurchaseOrderConfirmCooperateById(id);
    }

    /**
     * 查询采购订单确认列表
     * 
     * @param purchaseOrderConfirmCooperate 采购订单确认
     * @return 采购订单确认
     */
    @Override
    public List<PurchaseOrderConfirmCooperate> selectPurchaseOrderConfirmCooperateList(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate)
    {
        return purchaseOrderConfirmCooperateMapper.selectPurchaseOrderConfirmCooperateList(purchaseOrderConfirmCooperate);
    }

    /**
     * 新增采购订单确认
     * 
     * @param purchaseOrderConfirmCooperate 采购订单确认
     * @return 结果
     */
    @Override
    public int insertPurchaseOrderConfirmCooperate(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate)
    {
        purchaseOrderConfirmCooperate.setCreateTime( DateUtils.getNowDate());
        return purchaseOrderConfirmCooperateMapper.insertPurchaseOrderConfirmCooperate(purchaseOrderConfirmCooperate);
    }

    /**
     * 修改采购订单确认
     * 
     * @param purchaseOrderConfirmCooperate 采购订单确认
     * @return 结果
     */
    @Override
    public int updatePurchaseOrderConfirmCooperate(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate)
    {
        purchaseOrderConfirmCooperate.setUpdateTime(DateUtils.getNowDate());
        return purchaseOrderConfirmCooperateMapper.updatePurchaseOrderConfirmCooperate(purchaseOrderConfirmCooperate);
    }

    /**
     * 批量删除采购订单确认
     * 
     * @param ids 需要删除的采购订单确认主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrderConfirmCooperateByIds(Long[] ids)
    {
        return purchaseOrderConfirmCooperateMapper.deletePurchaseOrderConfirmCooperateByIds(ids);
    }

    /**
     * 删除采购订单确认信息
     * 
     * @param id 采购订单确认主键
     * @return 结果
     */
    @Override
    public int deletePurchaseOrderConfirmCooperateById(Long id)
    {
        return purchaseOrderConfirmCooperateMapper.deletePurchaseOrderConfirmCooperateById(id);
    }

    @Override
    public Map<String, Object> selectPurchaseOrderPreviewById(Long id, LoginUser loginUser) {

        Map<String, Object> map = new HashMap<>();
        //查询采购单信息
        PurchaseOrder purchaseOrder = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderById(id);
        map.put("purchaseOrder", purchaseOrder);

        //查询采购单明细
        List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderDetailById(id);
        //采购单明细数据处理
        initPurchaseOrderDetailData(purchaseOrderDetailList,purchaseOrder);
        map.put("purchaseOrderDetailList", purchaseOrderDetailList);

        //查询审核人和复审人
        Map<String, Object> param = purchaseOrderConfirmCooperateMapper.getPurchaseOrderApprove(purchaseOrder.getId());
        if (null != param) {
            map.put("approveUser", MapUtils.getString( param, "approveUser","" ) );
            map.put("reviewUser", MapUtils.getString( param, "reviewUser","" ));
        }else {
            map.put("approveUser", "");
            map.put("reviewUser", "");
        }

        //获取条码文件地址
        String ossUploadFileLink = ossUploadFileService.selectFileUrlByRelationIdUpload(purchaseOrder.getId(), FileBusinessEnum.PURCHASE_ORDER_PREVIEW_BAR_CODE.getBusinessCode()
                ,purchaseOrder.getInvoice(),loginUser.getUser());
        map.put("ossUploadFileLink", ossUploadFileLink);

        //获取采够收货仓库信息
        PurchaseWarehouseContact szWarehouseContact = purchaseOrderService.getOnePurchaseWarehouseContactByType("SZ",purchaseOrder.getCreatedAt());
        PurchaseWarehouseContact ywWarehouseContact = purchaseOrderService.getOnePurchaseWarehouseContactByType("YW",purchaseOrder.getCreatedAt());
        map.put("szWarehouseContact", szWarehouseContact);
        map.put("ywWarehouseContact", ywWarehouseContact);

        //目的仓国家名称查询 -- 目的仓国家代号
        String targetWarehouseCountrySymbol = purchaseOrderConfirmCooperateMapper.findTargetWarehouseCountrySymbolById(purchaseOrder.getId());
        map.put("targetWarehouseCountrySymbol",targetWarehouseCountrySymbol);

        //币种符号显示
        String currencySymbol = purchaseOrderService.getCurrencySymbol( purchaseOrder.getCurrency() );
        map.put("currencySymbol",currencySymbol);

        return map;
    }

    @Override
    public void exportPdf(Long id, LoginUser loginUser, HttpServletResponse response) {
        File file = null;
        File purchasePdfFile = null;
        String filePath = getFilePath();
        try {
            //查询采购单信息
            PurchaseOrder purchaseOrder = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderById(id);

            //查询采购单明细
            List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderDetailById(id);
            //采购单明细数据处理
            initPurchaseOrderDetailData(purchaseOrderDetailList,purchaseOrder);

            //查询审核人和复审人
            Map<String, Object> param = purchaseOrderConfirmCooperateMapper.getPurchaseOrderApprove(purchaseOrder.getId());
            if (null != param){
                purchaseOrder.setApproveUser(MapUtils.getString( param, "approveUser","" ));
                purchaseOrder.setReviewUser(MapUtils.getString( param, "reviewUser","" ));
            }

            //查询供应商
            VendorMaster vendorMaster = vendorMasterService.selectByPrimaryKey(purchaseOrder.getVendorId());

            //获取条码文件地址
            String ossUploadFileLink = ossUploadFileService.selectFileUrlByRelationIdUpload(purchaseOrder.getId(), FileBusinessEnum.PURCHASE_ORDER_PREVIEW_BAR_CODE.getBusinessCode()
                    ,purchaseOrder.getInvoice(),loginUser.getUser());

            //pdf临时文件
            file = ossUploadFileService.getFile( ossUploadFileLink, purchaseOrder.getInvoice(), AppConstant.PNG_FILE_SUFFIX,filePath );

            purchasePdfFile = ossUploadFileService.getTempFile( purchaseOrder.getInvoice(), AppConstant.PDF_FILE_SUFFIX,filePath );

            //获取采够收货仓库信息
            PurchaseWarehouseContact szWarehouseContact = purchaseOrderService.getOnePurchaseWarehouseContactByType("SZ",purchaseOrder.getCreatedAt());
            PurchaseWarehouseContact ywWarehouseContact = purchaseOrderService.getOnePurchaseWarehouseContactByType("YW",purchaseOrder.getCreatedAt());

            //目的仓国家名称查询 -- 目的仓国家代号
            String targetWarehouseCountrySymbol = purchaseOrderConfirmCooperateMapper.findTargetWarehouseCountrySymbolById(purchaseOrder.getId());

            //币种符号显示
            String currencySymbol = purchaseOrderService.getCurrencySymbol( purchaseOrder.getCurrency() );

            PurchasePDF pp = new PurchasePDF(purchaseOrderDetailList,szWarehouseContact,ywWarehouseContact, purchaseOrder, vendorMaster,
                    "", "", "0",file.getPath(), 6.8,targetWarehouseCountrySymbol,purchasePdfFile,currencySymbol);
            String s = pp.generatePDF();
            //下载文件
            FileUtil.downloadFile(purchasePdfFile, response);
        }catch (Exception e){
            logs.error( "下载采购单pdf文件异常",e );
        }finally {
            if (file != null && file.exists()) {
                file.delete(); // 确保删除临时文件
            }
            if (purchasePdfFile != null && purchasePdfFile.exists()) {
                purchasePdfFile.delete(); // 确保删除临时文件
            }
        }
    }

    @Override
    public OssUploadFileTfg exportReceipt(Long id, HttpServletResponse response) {
        MyAssert.isNull( id, "参数不能为空" );
        PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderConfirmCooperateById( id );
        MyAssert.isNull( purchaseOrderConfirmCooperate, "未查询到有效数据" );
        OssUploadFileTfg ossUploadFileTfg = null;
        if ( PurchaseOrderConfirmCooperateDataStatus.CONFIRMED.getCode().equals( purchaseOrderConfirmCooperate.getDataStatus() ) ){
           ossUploadFileTfg = ossUploadFileTfgService.queryOneByRelationId( id, FileBusinessEnum.PURCHASE_ORDER_CONFIRM_COOPERATE_UPLOAD.getBusinessCode() );
        }else {
           ossUploadFileTfg = ossUploadFileTfgService.queryOneByRelationId( id, FileBusinessEnum.PURCHASE_VENDOR_TEAM_WORK_FILE.getBusinessCode() );
        }
        if (ossUploadFileTfg != null){
            String generatorOuterLink = dubboFileService.generatorOuterLink( ossUploadFileTfg.getOssId() );
            logs.info( "回单文件外链：{}", generatorOuterLink );
            ossUploadFileTfg.setGeneratorOuterLink( generatorOuterLink );
            return ossUploadFileTfg;
        }
        return new OssUploadFileTfg();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importReceiptData(MultipartFile file, Long id, LoginUser loginUser) throws Exception{

        if (file == null){
            throw new ServiceException( "上传文件不能为空" );
        }
        if (id == null){
            throw new ServiceException( "关联数据不能为空" );
        }
        PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderConfirmCooperateById( id );
        if (purchaseOrderConfirmCooperate == null){
            throw new ServiceException( "订单确认数据不存在" );
        }
        if (purchaseOrderConfirmCooperate.getDataStatus() != null && PurchaseOrderConfirmCooperateDataStatus.INVALID.getCode().equals( purchaseOrderConfirmCooperate.getDataStatus() )){
            throw new ServiceException( "订单确认数据已失效" );
        }
        if (purchaseOrderConfirmCooperate.getDataStatus() != null && PurchaseOrderConfirmCooperateDataStatus.CONFIRMED.getCode().equals( purchaseOrderConfirmCooperate.getDataStatus() )){
            throw new ServiceException( "订单确认数据已回签" );
        }

        //修改状态为已回签
        purchaseOrderConfirmCooperate.setDataStatus( PurchaseOrderConfirmCooperateDataStatus.CONFIRMED.getCode() );
        purchaseOrderConfirmCooperate.setUpdateBy( loginUser.getUsername() );
        purchaseOrderConfirmCooperate.setUpdateTime( new Date() );
        purchaseOrderConfirmCooperate.setCounterSignatureTime( new Date() );
        purchaseOrderConfirmCooperate.setVersion( new Date() );
        purchaseOrderConfirmCooperate.setCounterSignatureBy( loginUser.getUsername() );
        purchaseOrderConfirmCooperateMapper.updatePurchaseOrderConfirmCooperate( purchaseOrderConfirmCooperate );
        //上传文件
        OssUploadFileTfg ossUploadFileTfg = ossUploadFileTfgService.uploadFile( file,id, purchaseOrderConfirmCooperate.getInvoice(),
                FileBusinessEnum.PURCHASE_ORDER_CONFIRM_COOPERATE_UPLOAD, loginUser.getUser(), "回单文件", null );
        if (ossUploadFileTfg == null){
            throw new ServiceException( "回单文件上传失败" );
        }
        //关联回单文件
        PurchaseVendorReceipts purchaseVendorReceipts = initPurchaseVendorReceipts( purchaseOrderConfirmCooperate, loginUser, ossUploadFileTfg );
        purchaseOrderConfirmCooperateMapper.insertPurchaseVendorReceipts(purchaseVendorReceipts);

    }


    private PurchaseVendorReceipts initPurchaseVendorReceipts(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate , LoginUser loginUser,OssUploadFileTfg ossUploadFileTfg) {
        PurchaseVendorReceipts purchaseVendorReceipts = new PurchaseVendorReceipts();
        purchaseVendorReceipts.setFileName(ossUploadFileTfg.getFileName());
        purchaseVendorReceipts.setSize(ossUploadFileTfg.getFileSize());
        purchaseVendorReceipts.setPath(null);
        purchaseVendorReceipts.setFileSource( FileSourceType.OSS.getCode());
        purchaseVendorReceipts.setOssId(ossUploadFileTfg.getOssId());
        purchaseVendorReceipts.setOrderId( purchaseOrderConfirmCooperate.getPurchaseOrderId() );
        purchaseVendorReceipts.setCreateBy(loginUser.getUsername() );
        return purchaseVendorReceipts;
    }


    @Override
    public void exportBatchCode(List<Long> idList, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request) {
        MyAssert.isEmptyList( idList, "关联数据不能为空" );
        File file = null;
        File QRCodeFile = null;
        File zipFile = null;
        String returnUrl=null;
        // 二维码写入目录
        String batchCodePath = getFilePath();
        String separator = getSeparator();
        try {

            List<PurchaseBatchCode> purchaseBatchCodeList = purchaseBatchCodeService.selectByIdList(idList);
            //校验权限
            PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = new PurchaseOrderConfirmCooperate();
            for (PurchaseBatchCode purchaseBatchCode : purchaseBatchCodeList) {
                purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderConfirmCooperateByOrderId( purchaseBatchCode.getPurchaseOrderId() );
                if (purchaseOrderConfirmCooperate == null){
                    throw new ServiceException( "订单确认数据不存在" );
                }
            }
            StringBuilder id = new StringBuilder();
            SimpleDateFormat sdnow = new SimpleDateFormat(DateUtils.YYYYMMDDHHMMSS);
            //生成1-100000的随机数字符串
            Random random = new Random();
            int randomNum = random.nextInt(10000) + 1;
            id.append( DateUtils.getFormatDate(new Date(), sdnow) + randomNum );
            zipFile = ossUploadFileService.getTempFile( purchaseOrderConfirmCooperate.getPurchaseInvoice(), AppConstant.ZIP_FILE_SUFFIX,batchCodePath );

            MyAssert.isEmptyList( purchaseBatchCodeList, "没有批次码数据" );

            // 二维码写入目录
            String poBatchCodePath = batchCodePath + File.separator + PurchaseBatchCodeService.poBatchCodePath + File.separator  + id + File.separator;

            for(PurchaseBatchCode purchaseBatchCode:purchaseBatchCodeList) {
                //加上PO 号和SKU及创建日期
                SimpleDateFormat sd = new SimpleDateFormat(DateUtils.YYYYMMDDHHMMSS);
                String code = "PO-" + purchaseBatchCode.getSku() + "-" + DateUtils.getFormatDate(purchaseBatchCode.getCreateTime(), sd);
                // 生成条形码
                String ossUploadFileLink = ossUploadFileService.selectFileUrlByRelationIdUpload(purchaseBatchCode.getId(), FileBusinessEnum.PURCHASE_BATCH_CODE_FILE.getBusinessCode()
                        ,purchaseBatchCode.getBatchCode(),loginUser.getUser());

                //pdf临时文件
                file = ossUploadFileService.getFile( ossUploadFileLink, purchaseBatchCode.getBatchCode(), AppConstant.PNG_FILE_SUFFIX, batchCodePath );

                purchaseBatchCode.setFileName(file.getName());
                //purchaseBatchCode.setFnskuCountryName(CountryCode.getTextByCode(purchaseBatchCode.getFnskuCountry()));
                List<PurchaseBatchCode> batchCodeList = new ArrayList<>();
                batchCodeList.add(purchaseBatchCode);

                // 生成二维码
                String smsSkuQRCodeFileName = purchaseBatchCode.getSmsSku() + AppConstant.PNG_FILE_SUFFIX;
                String smsSkuCustom = purchaseBatchCode.getSmsSku()+"-0"+"-NEW";
                QRCodeFile = ossUploadFileService.getTempFile( purchaseBatchCode.getSmsSku(), AppConstant.PNG_FILE_SUFFIX ,batchCodePath);

                QRCodeUtil.generateFile(smsSkuCustom, AppConstant.BATCH_CODE_QR_CODE_WIDTH, AppConstant.BATCH_CODE_QR_CODE_HEIGHT, QRCodeFile.getParent(), QRCodeFile.getName());
                purchaseBatchCode.setSmsSkuQRCodeFileName(QRCodeFile.getName());
                // 参数
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("batchCodeList", batchCodeList);
                // 导出pdf
                String fileName = DownloadUtils.getFileName(request, purchaseBatchCode.getBatchCode() + "-" + code + AppConstant.PDF_FILE_SUFFIX);
                // 模板转为html
                String htmlContent = FreemarkerUtil.htmlGenerate("PurchaseBatchCode.ftl", dataMap);
                // html转为pdf
                ITextRenderer renderer = new ITextRenderer();
                renderer.setDocumentFromString(htmlContent);

                // 解决中文支持问题
                ITextFontResolver fontResolver = renderer.getFontResolver();
                fontResolver.addFont(File.separator + "ttf" + File.separator + "NotoSansHans-Medium.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
                // 解决图片的相对路径问题
                SharedContext sharedContext = renderer.getSharedContext();
                // 条码输出目录
                sharedContext.setBaseURL("file:"+ batchCodePath + separator);

                renderer.layout();
                File path = new File(poBatchCodePath + "/pdf");
                if(!path.exists()){
                    path.mkdirs();
                }
                String url =  path.getPath() + "/" + fileName;
                renderer.createPDF(new FileOutputStream(new File(url)));
            }
            File purchasePdfFile = new File(poBatchCodePath + "/zip/" + purchaseOrderConfirmCooperate.getPurchaseInvoice()+".zip");
            //创建一个压缩的文件
            if (QRCodeFile != null){
                FileUtil.compressToZip(poBatchCodePath + "/pdf", poBatchCodePath  + "/zip" , purchaseOrderConfirmCooperate.getPurchaseInvoice()+".zip");
            }
            //下载文件
            FileUtil.downloadFile(purchasePdfFile, response);
        }catch (ServiceException e){
            logs.error("批次码管理下载ZIP异常", e);
            throw new ServiceException( e.getMessage());
        }catch (Exception e){
            logs.error("批次码管理下载ZIP异常", e);
            throw new RuntimeException( "下载异常" );
        }finally {
            if (file != null && file.exists()) {
                file.delete(); // 确保删除临时文件
            }
            if (QRCodeFile != null && QRCodeFile.exists()) {
                QRCodeFile.delete(); // 确保删除临时文件
            }
            if (zipFile != null && zipFile.exists()) {
                zipFile.delete(); // 确保删除临时文件
            }
        }

    }

    @Override
    public void exportFnskuCode(List<PurchaseOrderDetail> detailList, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request) {

        try {
            MyAssert.isEmptyList( detailList, "下载数据不能为空" );
            //校验权限
            PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = checkAuth( detailList );
            List<PurchaseOrderDetail> data = new ArrayList();

            for(PurchaseOrderDetail m : detailList){
                if(m.getQty() > 0){
                    String mappingType = purchaseOrderService.selectMappingType(m.getSku(),m.getFnsku());
                    m.setMappingType(mappingType);
                    m.setBllingCn(purchaseOrderConfirmCooperate.getPurchaseInvoice());
                    data.add(m);
                }
            }
            // 二维码写入目录
            String batchCodePath = getFilePath();

            // 生成FNSKU条码，打包
            String zipfile = this.createBatchFnskuBarcodeZip(data,batchCodePath);
            if(StringUtils.isNotBlank(zipfile)){
                response.setContentType("APPLICATION/OCTET-STREAM");
                response.setHeader("Content-Disposition", "attachment; filename=" + purchaseOrderConfirmCooperate.getPurchaseInvoice() + "_fnsku.zip");
                FileUtil.downloadFile(zipfile, response);
                response.flushBuffer();
            }else{
                response.setCharacterEncoding("GBK");
                response.getWriter().print("相关申购单未指定FNSKU，下载FNSKU条码失败。");
                response.flushBuffer();
            }

        }catch (ServiceException e){
            logs.error("导出FNSKU条码异常", e);
        }catch (Exception e){
            logs.error("导出FNSKU条码异常", e);
        }
    }

    public String getFilePath(){
        // 二维码写入目录
        String osType = System.getProperty(AppConstant.OS_NAME).toUpperCase();
        String batchCodePath;
        if( osType .contains(AppConstant.OS_TYPE) ){
            batchCodePath = AppConstant.BARCODE_FILE_PATH_LOCAL;
        }else{
            batchCodePath = sysConfigService.selectConfigByKey( AppConstant.BARCODE_FILE_PATH_KEY );
        }
        return batchCodePath;
    }

    public String getSeparator(){
        // 二维码写入目录
        String osType = System.getProperty(AppConstant.OS_NAME).toUpperCase();
        String separator;
        if( osType .contains(AppConstant.OS_TYPE) ){
            separator = AppConstant.STRING_SLASH;
        }else{
            separator = File.separator;
        }
        return separator;
    }

    private PurchaseOrderConfirmCooperate checkAuth(List<PurchaseOrderDetail> detailList) {
        PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = new PurchaseOrderConfirmCooperate();
        for (PurchaseOrderDetail purchaseOrderDetail : detailList) {
            MyAssert.isNull( purchaseOrderDetail.getOrderId(), "订单ID不能为空" );
            purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderConfirmCooperateByOrderId( purchaseOrderDetail.getOrderId() );
            if (purchaseOrderConfirmCooperate == null){
                throw new ServiceException( "订单确认数据不存在" );
            }
        }
        return purchaseOrderConfirmCooperate;
    }


    @Override
    public void exportMrpCode(List<PurchaseOrderDetail> purchaseOrderDetailList, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request)throws Exception {
        try {
            List<PurchaseOrderDetail> newList = new ArrayList<>();
            for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetailList) {
                List<PurchaseOrderDetail> collect = newList.stream().filter( item -> item.getSku().equals( purchaseOrderDetail.getSku() ) ).collect( Collectors.toList() );
                if (CollectionUtils.isEmpty( collect )){
                    newList.add( purchaseOrderDetail );
                }
            }
            MyAssert.isEmptyList( newList, "下载数据不能为空" );
            //校验权限
            PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = checkAuth( newList );
            PurchaseOrder purchaseOrder = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderById( purchaseOrderConfirmCooperate.getId() );
            if (CollectionUtils.isEmpty( newList )){
                throw new ServiceException( "没有数据可导出" );
            }
            String filePath = getFilePath() + getSeparator() + AppConstant.PURCHASE_ORDER_CONFIRM_MRP_PATH + getSeparator() + purchaseOrderConfirmCooperate.getPurchaseInvoice();
            List<String> skuList = newList.stream().map( PurchaseOrderDetail::getSku ).collect( Collectors.toList() );
            // 生成FNSKU条码，打包
            String zipfile = this.createPurchaseOrderMRPINPDFZip(purchaseOrderConfirmCooperate.getPurchaseInvoice(),skuList,purchaseOrder,filePath);
            if(StringUtils.isNotBlank(zipfile)){
                String ext = zipfile.substring(zipfile.lastIndexOf(".") + 1).toUpperCase();
                String orgFileName = zipfile.substring(zipfile.lastIndexOf("/") + 1).toUpperCase();
                String fileName = ext.equals("ZIP") ? ( purchaseOrderConfirmCooperate.getPurchaseInvoice() + "_MRP_IN.zip") :  (purchaseOrderConfirmCooperate.getPurchaseInvoice() + "_"+orgFileName);
                //下载文件
                File purchasePdfFile = new File(zipfile);
                //FileUtil.downloadFile(purchasePdfFile, response);
                FileUtil.downloadFile(zipfile, fileName, response);
                response.flushBuffer();
                FileUtil.deleteFolder(zipfile);
            }else{
                response.setCharacterEncoding("GBK");
                response.getWriter().print("无MRP标条码，下载MRP条码失败。");
                response.flushBuffer();
            }
        }catch (ServiceException e){
            logs.error("下载印度MRP标失败", e);
        }catch (Exception e) {
            response.setCharacterEncoding("GBK");
            response.getWriter().print("下载失败:"+e);
            response.flushBuffer();
            logs.info("下载印度MRP标失败", e);
        }
    }

    @Override
    public void exportWmsCode(List<PurchaseOrderDetail> detailList, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request) {
        String filePath = "";
        File QRCodeFile = null;
        String separator = getSeparator();
        // 二维码写入目录
        String qrCodePath = getFilePath();
        try {
            MyAssert.isEmptyList( detailList, "下载数据不能为空" );
            PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = checkAuth( detailList );
            //查询存在wms码的数据
            filePath = getFilePath() + File.separator + AppConstant.PURCHASE_ORDER_CONFIRM_WMS_PATH + File.separator + purchaseOrderConfirmCooperate.getId() + File.separator;
            for (PurchaseOrderDetail purchaseOrderDetail : detailList) {

                String brandName = purchaseOrderConfirmCooperateMapper.getItemBrandById( purchaseOrderDetail.getBrandId() );
                //查询wms sku
                String wmsSku =  purchaseOrderConfirmCooperateMapper.selectWmsSku(purchaseOrderDetail.getSku());
                if (StringUtils.isBlank( wmsSku )){
                    wmsSku = "没有wms映射关系";
                }
                // 生成二维码
                String smsSkuQRCodeFileName = wmsSku+".png";
                String smsSkuCustom = wmsSku+"-0"+"-NEW";
                QRCodeFile = ossUploadFileService.getTempFile( smsSkuQRCodeFileName, AppConstant.PNG_FILE_SUFFIX ,qrCodePath);
                QRCodeUtil.generateFile(smsSkuCustom, 150, 150, QRCodeFile.getParent(), QRCodeFile.getName());

                String fileName = DownloadUtils.getFileName(request, purchaseOrderDetail.getSku()  + ".pdf");

                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put( "wmsSkuQRCodeFileName",QRCodeFile.getName() );
                dataMap.put( "smsSkuCustom",smsSkuCustom );
                dataMap.put( "sku",purchaseOrderDetail.getSku()
                );
                dataMap.put( "brand",brandName );
                // 模板转为html
                String htmlContent = FreemarkerUtil.htmlGenerate("wmsSku.ftl", dataMap);
               // html转为pdf
                ITextRenderer renderer = new ITextRenderer();
                renderer.setDocumentFromString(htmlContent);

                // 解决中文支持问题
                ITextFontResolver fontResolver = renderer.getFontResolver();
                fontResolver.addFont(File.separator + "ttf" + File.separator + "NotoSansHans-Medium.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
                // 解决图片的相对路径问题
                SharedContext sharedContext = renderer.getSharedContext();
                // 条码输出目录
                sharedContext.setBaseURL("file:"+ qrCodePath + separator);

                renderer.layout();
                File path = new File(filePath + "/pdf");
                if(!path.exists()){
                    path.mkdirs();
                }
                String url =  path.getPath() + "/" + fileName;
                renderer.createPDF(new FileOutputStream(new File(url)));
            }
            File purchasePdfFile = new File(filePath + "/zip/" + purchaseOrderConfirmCooperate.getPurchaseInvoice()+".zip");
            //创建一个压缩的文件
            if (QRCodeFile != null){
                FileUtil.compressToZip(filePath + "/pdf", filePath  + "/zip" , purchaseOrderConfirmCooperate.getPurchaseInvoice()+".zip");
            }
            //下载文件
            FileUtil.downloadFile(purchasePdfFile, response);
        }catch (ServiceException e){
            logs.error("下载WMS条码异常", e);
        }catch (Exception e){
            logs.error( "下载WMS条码异常",e );
        }finally {
            FileUtil.deleteFileAll(filePath );
        }
    }


    @Override
    public List<PurchaseBatchCode> selectPurchaseBatchCodeList(String invoice, LoginUser loginUser) {
        return purchaseBatchCodeService.selectByOrderId(invoice);
    }

    public List<PurchaseOrderDetail> removeNullElements(List<PurchaseOrderDetail> purchaseOrderDetails) {
    return purchaseOrderDetails.stream()
                               .filter(Objects::nonNull)
                               .collect(Collectors.toList());
}

    @Override
    public List<PurchaseOrderDetail> selectPurchaseFnskuList(Long purchaseOrderId, LoginUser loginUser) {
        List<PurchaseOrderDetail> purchaseOrderDetails = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderDetailFnskuByOrderId( purchaseOrderId );
        return removeNullElements(purchaseOrderDetails);
    }

    @Override
    public List<PurchaseOrderDetail> selectPurchaseMrpList(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate, LoginUser loginUser) {
        return purchaseOrderConfirmCooperateMapper.selectMrpList(purchaseOrderConfirmCooperate.getPurchaseOrderId(),purchaseOrderConfirmCooperate.getTargetCountryCode());
    }

    @Override
    public PurchaseOrderConfirmCooperate selectPurchaseOrderInfoByConfirmId(Long id) {
        PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderConfirmCooperateById( id );
        String targetCountryCode = purchaseOrderConfirmCooperateMapper.selectTargetCountryCode(purchaseOrderConfirmCooperate.getPurchaseOrderId());
        purchaseOrderConfirmCooperate.setTargetCountryCode( targetCountryCode );
        return purchaseOrderConfirmCooperate;
    }

    @Override
    public List<PurchaseOrderDetail> selectPurchaseWmsList(Long id, LoginUser loginUser) {
        //查询存在wms码的数据
        return  purchaseOrderConfirmCooperateMapper.selectPurchaseOrderDetailWmsById( id );
    }

    @Override
    public void exportUpcCode(String itemMappingIdList, HttpServletResponse response) {
        if (StringUtils.isBlank( itemMappingIdList )){
            return;
        }
        try(ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            String filename = "下载UPC文件-" + System.currentTimeMillis() + ".zip";
            // 需要编码否则中文乱码
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String(filename.getBytes("GB2312"), "ISO-8859-1"));
            response.setContentType("application/zip;charset=utf-8");
            response.setCharacterEncoding("UTF-8");
            List<Long> idList = Stream.of( itemMappingIdList.split( "," ) ).map( Long::parseLong ).collect( Collectors.toList() );
            // 输出流直接用ZipOutputStream包裹，这样直接输出压缩后的流。减少服务器生成压缩文件步骤。
            ossUploadFileTfgService.downLoadUpcFile( idList, FileBusinessEnum.ITEM_MAPPING_UPC_FILE.getBusinessCode(),zipOutputStream );
            zipOutputStream.close();
        }catch (Exception e){
            logs.error("导出UPC文件异常", e);
        }

    }

    @Override
    public void exportReceiptDownloads(List<Long> ids, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request) {
        MyAssert.isEmptyList( ids, "参数不能为空" );
        for (Long id : ids) {
            PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateMapper.selectPurchaseOrderConfirmCooperateById( id );
            MyAssert.isNull( purchaseOrderConfirmCooperate, "未查询到有效数据" );
        }
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())){
            ossUploadFileTfgService.downloadZip(  ids, FileBusinessEnum.PURCHASE_VENDOR_TEAM_WORK_FILE.getBusinessCode(),zipOutputStream);
        }catch (Exception e){
            logs.error("批量下载回单文件异常", e);
        }
    }

    public String createPurchaseOrderMRPINPDFZip(String poInvoice, List<String> skuList,PurchaseOrder purchaseOrder,String filePath) throws Exception {

        List<ItemMRPIN> itemMRPINList = purchaseOrderConfirmCooperateMapper.findDataVersionMRP(purchaseOrder.getReviewTime(), skuList);
        if(CollectionUtils.isEmpty(itemMRPINList)) {
            logs.warn( "采购单:"+poInvoice+"下该批sku:"+skuList.toString()+"无MRP标记录" );
            return null;
            //throw new Exception("采购单:"+poInvoice+"下该批sku:"+skuList.toString()+"无MRP标记录");
        }
        List<Long> itemIdList = itemMRPINList.stream().map(ItemMRPIN::getItemId).collect( Collectors.toList());
        List<Map<String,Object>> skuQtyList = purchaseOrderConfirmCooperateMapper.findPurchaseSkuQty(poInvoice, itemIdList);
        Map<Long, Integer> skuQtyMap = skuQtyList.stream().collect(Collectors.toMap(o->MapUtils.getLong(o, "itemId"), o->MapUtils.getInteger(o, "qty")));
        Integer onePageQty = 42;
        for (ItemMRPIN itemMRP : itemMRPINList) {
            Integer qty = MapUtils.getInteger(skuQtyMap, itemMRP.getItemId(), onePageQty);
            Integer remainder = qty % onePageQty;
            if(remainder > 0) {
                qty = qty - remainder + onePageQty;
            }

            itemMRP.setPageQty(qty/onePageQty);
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMMM, yyyy", Locale.ENGLISH);
        return createMRPINPDFZip(itemMRPINList, dateFormat.format(purchaseOrder.getReviewTime()),filePath);
    }

    public String createMRPINPDFZip(List<ItemMRPIN> itemMRPINList, String importMonthYear, String barcodePath) throws Exception {
        // date:2022-01-12 #9937 【印度MRP表】Import Month & Year字段值，改为下载时间的年月
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMMM, yyyy", Locale.ENGLISH);
        importMonthYear = dateFormat.format(Calendar.getInstance().getTime());

        // 打包
        String zipPath = null;
        if (CollectionUtils.isNotEmpty(itemMRPINList)) {
            zipPath = barcodePath + "/zip/mrp";

            File zipFilePath = new File(zipPath);
            if (!zipFilePath.exists()) {
                zipFilePath.mkdirs();
            }

            File pdfFilePath = new File(barcodePath + "/pdf/mrp");
            if (!pdfFilePath.exists()) {
                pdfFilePath.mkdirs();
            }

            // 创建 ZIP 文件
            String zipFileName = zipPath + "/印度MRP标_" + new SimpleDateFormat("yyMMddHHmmss").format(new Date()) + ".zip";
            File zipFile = new File(zipFileName);
            if (zipFile.exists()) {
                zipFile.delete();
            }
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFile));

            for (ItemMRPIN itemMRP : itemMRPINList) {
                String filePath = barcodePath + "/pdf/mrp/MRP_IN_" + itemMRP.getSku() + ".pdf";
                // 创建 PDF 文件
                File file = new File(filePath);
                if (file.exists()) {
                    file.delete();
                }
                file.createNewFile();
                itemMRP.setImportMonthYear(importMonthYear);
                createMRPINPDF(barcodePath, file, itemMRP);

                // 将 PDF 文件添加到 ZIP 中
                FileUtil.zipFile(file.getAbsolutePath(), out);
                file.delete(); // 删除临时 PDF 文件
            }

            if (out != null) {
                out.close(); // 关闭 ZIP 输出流
            }

            // 返回 ZIP 文件路径
            return zipFileName;
        }

        return zipPath;
    }

    private void createMRPINPDF(String barcodePath, File file, ItemMRPIN itemMRP) throws Exception {
        //1.建立A4纸张大小document
        // PageSize.A4
        Rectangle rt = new Rectangle(PageSize.A4.getHeight(), PageSize.A4.getWidth());
        //rt = rt.rotate();//横向显示
        Document document = new Document(rt);
        document.setMargins(0f, 0f, 0f, 0f);
        //3document和pdf对象关联
        PdfWriter pdfWriter = PdfWriter.getInstance(document, new FileOutputStream(file));

        //4打开文档
        document.open();
        document.addAuthor("THOUSAND SHORES");//作者
        PdfContentByte canvas = pdfWriter.getDirectContent();
        //5生成PDF文件
        this.generatePDF(document, canvas, itemMRP);

        //6.关闭文件流
        document.close();
        pdfWriter.close();
    }

    /**
     * 生成PDF文件
     * @param document
     * @param itemMRP
     * <AUTHOR>
     * @throws IOException
     * @throws DocumentException
     * @date:2021年12月17日
     */
    public void generatePDF(Document document, PdfContentByte canvas, ItemMRPIN itemMRP) throws DocumentException, IOException {
        BaseFont bfChinese = BaseFont.createFont(FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font smallfontChinese = new Font( bfChinese, 3.6f, Font.NORMAL );
        //表格
        PdfPTable table = createTable(6, Element.ALIGN_RIGHT);


        Phrase smalAddressPhrase = new Phrase();
        smalAddressPhrase.add(new Chunk(itemMRP.getSku(), smallfontChinese));
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk("Product name: "+itemMRP.getProductName(), smallfontChinese));
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk("Product Model: "+itemMRP.getProductModel(), smallfontChinese));
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk("MRP: "+itemMRP.getMrp(), smallfontChinese));
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk("Package Content (Net Qty): "+itemMRP.getPackageContent(), smallfontChinese));
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk("Country of Origin: "+itemMRP.getCountryOfOrigin(), smallfontChinese));
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk("Manufactured By: "+itemMRP.getManufacturedBy(), smallfontChinese));
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk("Manufacturing Month & Year:"+StringUtils.defaultString(itemMRP.getImportMonthYear(), ""), smallfontChinese));//新增
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk("Import Month & Year: "+StringUtils.defaultString(itemMRP.getImportMonthYear(), ""), smallfontChinese));
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk("Imported By: "+itemMRP.getImportedBy(), smallfontChinese));
        smalAddressPhrase.add(Chunk.NEWLINE);
        smalAddressPhrase.add(new Chunk(itemMRP.getCustomerCare(), smallfontChinese));


        BigDecimal pageWidth = new BigDecimal(String.valueOf(PageSize.A4.getWidth()));
        BigDecimal blankPageUnit = new BigDecimal("4.35").multiply(new BigDecimal("72")).divide(new BigDecimal("25.4"), 4, BigDecimal.ROUND_DOWN);
        BigDecimal baseHeight = pageWidth.divide(new BigDecimal("7"), 6, BigDecimal.ROUND_UP);

        PdfPCell cell = new PdfPCell();
        cell.setUseAscender(Boolean.TRUE);
        cell.setVerticalAlignment(Element.ALIGN_TOP);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPhrase(smalAddressPhrase);
        cell.setBorder(0);
        cell.setPadding(0);
//		cell.setBorderColor(BaseColor.BLACK);
//		cell.setBorderWidthLeft(0.1f);
//		cell.setBorderWidthBottom(0.1f);
//		cell.setBorderColorRight(BaseColor.BLACK);

        for(int i = 0; i < 7; i++) {
            cell.setFixedHeight(baseHeight.add(new BigDecimal("1.5")).floatValue());
            if(i == 0) {
                cell.setFixedHeight(baseHeight.subtract(blankPageUnit).floatValue());//第一行减去打印机默认留白区域
            }else {
                cell.setPaddingTop(0f);
                cell.setPaddingLeft(0f);
            }
            if(i == 6) {//最后一行
                cell.setVerticalAlignment(Element.ALIGN_BOTTOM);
                cell.setPaddingBottom(1f);
            }
            cell.setPaddingRight(12f);
            cell.setPaddingLeft(0f);
            table.addCell(cell);
            cell.setPaddingRight(5f);
            cell.setPaddingLeft(0f);
            table.addCell(cell);
            cell.setPaddingLeft(5f);
            table.addCell(cell);
            table.addCell(cell);
            table.addCell(cell);
            cell.setPaddingRight(0f);//最后一列
            cell.setPaddingLeft(6f);//最后一列
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            if(i == 6) {//最后一行
                cell.setVerticalAlignment(Element.ALIGN_BOTTOM);
                cell.setPaddingBottom(1f);
            }
            table.addCell(cell);
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        }
        Integer pageQty = (itemMRP.getPageQty() == null || itemMRP.getPageQty() < 1) ? 1 : itemMRP.getPageQty();
        for (int i = 0; i < pageQty; i++) {
            document.add(table);
        }
    }

    /**
     * 创建默认列宽,指定列数、水平的表格
     * @return
     * <AUTHOR>
     * @throws DocumentException
     * @date:2021年12月17日
     */
    public PdfPTable createTable(int colNumber, int align) throws DocumentException {
        PdfPTable table = new PdfPTable(colNumber);
        table.setHorizontalAlignment(align);
        // 设置表格上面空白宽度
        table.setSpacingBefore(0);
        // 设置表格下面空白宽度
        table.setSpacingAfter(0);

        //table.setWidthPercentage(100);//设置表格宽度比例为%100
        table.getDefaultCell().setBorder(0);
        table.getDefaultCell().setPadding(0);
        //A4纸张高度是297mm,现打印纸张高度是290mm
        BigDecimal printPageHeight = new BigDecimal("290").multiply(new BigDecimal("72")).divide(new BigDecimal("25.4"), 4, BigDecimal.ROUND_DOWN);
        BigDecimal blankPageUnit = new BigDecimal("4.35").multiply(new BigDecimal("72")).divide(new BigDecimal("25.4"), 4, BigDecimal.ROUND_DOWN);//打印机默认留白空间
        BigDecimal baseCellWidth = printPageHeight.divide(new BigDecimal("6"), 4, BigDecimal.ROUND_DOWN);
        BigDecimal smallbaseCellWidth = baseCellWidth.subtract(blankPageUnit);
        float[] columnWidths = new float[]{baseCellWidth.floatValue(), baseCellWidth.floatValue(),baseCellWidth.floatValue(), baseCellWidth.floatValue(), baseCellWidth.floatValue(), smallbaseCellWidth.floatValue()};
        table.setWidths(columnWidths);
        table.setTotalWidth(printPageHeight.add(new BigDecimal("2")).floatValue());
        table.setLockedWidth(true);
        return table;
    }

    private String createBatchFnskuBarcodeZip(List<PurchaseOrderDetail> fnskuList,String barcodePath)throws Exception {
        // 条码输出目录

        List<WarehouseBarcode> list = this.createFnskuBarcodeFile(fnskuList,barcodePath);

        // 打包
        String zipPath = null;

        if(list != null && !list.isEmpty()){

            zipPath = barcodePath + "/zip";

            File zipFilePath = new File(zipPath);
            if(!zipFilePath.exists()){
                zipFilePath.mkdirs();
            }else{
                zipFilePath = null;
            }

            zipPath = zipPath + "/批量小标FNSKU"+ new SimpleDateFormat("yyMMddHHmmss").format(new Date()) +".zip";
            File zipFile = new File(zipPath);
            if(zipFile.exists()) zipFile.delete();

            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFile));
            int index = 0;
            for(WarehouseBarcode barcode : list){
                for(PurchaseOrderDetail mapping : fnskuList){
                    if(barcode.getBarcode().equals(mapping.getFnsku())){
                        String invoice = mapping.getBllingCn().replaceAll("\\\\", "").replaceAll("/", "").replaceAll("\\*", "");
                        String sku = mapping.getSku();
                        String fnsku = mapping.getFnsku();
                        int qty = mapping.getQty();

                        // 单个 PDF文件
                        PdfReader pdfReader = new PdfReader(barcodePath + barcode.getPdfFile());

                        // 要合成的PDF文件
                        File pdfFile = new File(barcodePath + "/zip/" + invoice+"_"+sku+"_"+fnsku+"_"+qty + "_" + index++ + ".pdf");
                        if(pdfFile.exists()) pdfFile.delete();
                        OutputStream outPdf = new FileOutputStream(pdfFile);

                        // 开始绘制
                        Document document = new Document(new Rectangle(new Rectangle(612,792)),15,15,45,10);
                        PdfWriter pdfwriter = PdfWriter.getInstance(document, outPdf);
                        document.open();

                        PdfImportedPage page = pdfwriter.getImportedPage(pdfReader, 1);

                        Image image = Image.getInstance(page);
                        image.scalePercent(85, 80);
                        PdfPCell imgCell = new PdfPCell(image,false);
                        imgCell.setBorder(0);

                        // 批量复制的数量
                        int count = qty;

                        PdfPTable table = new PdfPTable(3);
                        table.setWidthPercentage(100);
                        table.setHorizontalAlignment( Element.ALIGN_LEFT);
                        table.setSpacingBefore(3);

                        table.addCell(imgCell);
                        table.addCell(imgCell);
                        table.addCell(imgCell);

                        for(int i=0;i<count/3;i++){
                            document.add(table);
                        }

                        // 剩余部分单独复制
                        PdfPTable lastTable = new PdfPTable(3);
                        lastTable.setWidthPercentage(100);
                        lastTable.setHorizontalAlignment(Element.ALIGN_LEFT);
                        lastTable.setSpacingBefore(3);
                        lastTable.getDefaultCell().setBorder(0);

                        if(count%3==2){
                            lastTable.addCell(imgCell);
                            lastTable.addCell(imgCell);
                            lastTable.addCell(" ");
                            document.add(lastTable);
                        }else if(count%3==1){
                            lastTable.addCell(imgCell);
                            lastTable.addCell(" ");
                            lastTable.addCell(" ");
                            document.add(lastTable);
                        }

                        document.close();
                        pdfwriter.close();
                        pdfReader.close();
                        FileUtil.zipFile(pdfFile.getAbsolutePath(), out);

                        pdfFile.delete();
                    }
                }

            }

            out.close();
        }

        return zipPath;
    }

    public List<WarehouseBarcode> createFnskuBarcodeFile(List<PurchaseOrderDetail> fnskuList,String barcodePath) throws Exception {
        /**
         * 新建一个字体,iText的方法 STSongStd-Light 是字体，在iTextAsian.jar 中以property为后缀
         * UniGB-UCS2-H 是编码，在iTextAsian.jar 中以cmap为后缀 H 代表文字版式是 横版， 相应的 V
         * 代表竖版
         */
        // BaseFont bfChinese = BaseFont.createFont( "STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED );
        BaseFont bfChinese = BaseFont.createFont(FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font fontChinese = new Font( bfChinese, 7 );
        // 条码对象
        List<WarehouseBarcode> warehouseBarcodeList = new ArrayList();

        // 填充模板字段
        for (int i = 0; i < fnskuList.size(); i++) {
            PurchaseOrderDetail mapping = fnskuList.get( i );
            String barcode = mapping.getFnsku();
            String description = StringUtils.isBlank( mapping.getFnskuRemarks() ) ? "" : mapping.getFnskuRemarks();

            // 按目录
            String vendor = barcodePath + "/pdf";
            File vendorPath = new File( vendor );
            if (!vendorPath.exists()) {
                vendorPath.mkdirs();
            } else {
                vendorPath = null;
            }

            // 要输出的PDF文件
            String pdf = "/" + barcode + ".pdf";
            File pdfFile = new File( vendor + pdf );
            if (pdfFile.exists()) pdfFile.delete();
            OutputStream outPdf = new FileOutputStream( pdfFile );

            // 开始绘制
            Document document = new Document( new Rectangle( 198, 85 ), 5, 5, 5, 5 );
            PdfWriter pdfwriter = PdfWriter.getInstance( document, outPdf );
            document.open();
            // BaseFont font = BaseFont.createFont( BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED );
            BaseFont font = BaseFont.createFont(FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

            // 表格
            PdfPTable table = new PdfPTable( 1 );
            table.setWidthPercentage( 100 );
            table.setHorizontalAlignment( Element.ALIGN_CENTER );

            // 生成条码图片字节数组
            byte[] barcodeByte = ossUploadFileService.createBarcode( barcode, 100, 5 );

            //生成条形码
            Image img = Image.getInstance( barcodeByte );
            //居中显示
            img.setAlignment( Image.ALIGN_CENTER );
            //边框
            img.setBorder( 0 );
            //显示位置，根据需要调整
            //img.setAbsolutePosition(-50, 25);
            //显示为原条形码图片大小的比例，百分比
            img.scalePercent( 40, 100 );

            PdfPCell imgCell = new PdfPCell( img );
            imgCell.setBorder( 0 );
            imgCell.setHorizontalAlignment( Element.ALIGN_LEFT );

            table.addCell( imgCell );

            // FNSKU编码
            PdfPCell barcodeCell = new PdfPCell( new Paragraph( barcode ) );
            barcodeCell.setBorder( 0 );
            barcodeCell.setHorizontalAlignment( Element.ALIGN_CENTER );
            table.addCell( barcodeCell );

            // title 24W Quick Charge 3.0, iCl...LG G5 V10, HTC 10 and More
            if (StringUtils.isNotBlank( description )) {
                if (description.length() > 51) {
                    description = description.substring( 0, 25 ) + "..." + description.substring( description.length() - 26 );
                }
            } else {
                description = " ";
            }

            PdfPCell titleCell = null;
            String regex = ".*[\\u4e00-\\u9faf].*";
            if (StringUtils.isNotBlank( description ) && Pattern.matches( regex, description )) {//如果有中文
                titleCell = new PdfPCell( new Paragraph( description, fontChinese ) );
            } else {
                titleCell = new PdfPCell( new Paragraph( description, new Font( font, 7 ) ) );
            }
            titleCell.setBorder( 0 );
            titleCell.setHorizontalAlignment( Element.ALIGN_LEFT );
            table.addCell( titleCell );

            // NEW
            if (!SkuBarCodeType.EAN.getCode().equals(mapping.getMappingType())){
                PdfPCell newCell = new PdfPCell( new Paragraph( "New" ) );
                newCell.setBorder( 0 );
                newCell.setHorizontalAlignment( Element.ALIGN_LEFT );
                table.addCell( newCell );
            }

            document.add( table );

            document.close();
            pdfwriter.close();

            // 记录条码记录
            WarehouseBarcode wb = new WarehouseBarcode();
            wb.setBarcode( barcode );
            wb.setBarcodeType( WarehouseBarcodeEnum.FNSKU.getCode() );
            wb.setPdfFile( "/pdf" + pdf );
            wb.setPngFile( null );

            warehouseBarcodeList.add( wb );

        }

        return warehouseBarcodeList;
    }

    /**
     * 初始化采购订单明细数据
     * 本方法主要用于计算和设置采购订单明细中各项财务数据，如不含税金额、税收金额等
     * 根据采购订单的价格类型（含税、未税、美元），处理方式有所不同
     *
     * @param purchaseOrderDetailList 采购订单明细列表，包含各个商品的明细数据
     * @param purchaseOrder 采购订单对象，包含订单级别的数据如价格类型
     */
    public void initPurchaseOrderDetailData(List<PurchaseOrderDetail> purchaseOrderDetailList, PurchaseOrder purchaseOrder) {

        // 初始化折扣总额、总金额和未税总金额
        BigDecimal discount = BigDecimal.ZERO;
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal notTaxAmountTotal = BigDecimal.ZERO;
        int totalQty = 0;

        // 遍历采购订单明细列表，对每个明细项进行处理
        for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetailList) {
            // 获取或默认仓库入库费
            BigDecimal warehouseInFee = Optional.ofNullable(purchaseOrderDetail.getWarehouseInFee()).orElse(BigDecimal.ZERO);
            // 获取或默认折扣额
            BigDecimal discountDetail = Optional.ofNullable(purchaseOrderDetail.getDiscount()).orElse(BigDecimal.ZERO);
            // 获取或默认明细项总额
            BigDecimal totalDetail = Optional.ofNullable(purchaseOrderDetail.getTotal()).orElse(BigDecimal.ZERO);
            //累加折扣总额、总金额,总数量
            totalQty += Optional.ofNullable(purchaseOrderDetail.getQty()).orElse(0);
            discount = discount.add(discountDetail);
            total = total.add(totalDetail);

            // 根据采购订单的价格类型进行不同的处理
            if (PriceType3.TAX.getValue() == purchaseOrder.getPriceType()) {
                // 计算不含税单价并设置
                BigDecimal exFactoryPrice = purchaseOrderDetail.getTaxUnitPrice().subtract(warehouseInFee).setScale(2, RoundingMode.HALF_UP);
                purchaseOrderDetail.setExFactoryPrice(exFactoryPrice);
                purchaseOrderDetail.setPurchasePrice(purchaseOrderDetail.getTaxUnitPrice());

                // 计算未税金额并设置
                BigDecimal notTaxAmount = totalDetail
                        .divide(purchaseOrderDetail.getTaxRate().add(new BigDecimal(100)).divide(new BigDecimal(100), 6, RoundingMode.HALF_UP), 2, RoundingMode.HALF_UP);
                purchaseOrderDetail.setExcludeTaxAmount(notTaxAmount);

                // 计算税额并设置
                BigDecimal taxAmount = purchaseOrderDetail.getTotal().subtract(notTaxAmount).setScale(2, RoundingMode.HALF_UP);
                purchaseOrderDetail.setTaxAmount(taxAmount);

                notTaxAmountTotal = notTaxAmountTotal.add(notTaxAmount);

                purchaseOrderDetail.setDescription( purchaseOrderDetail.getDescriptionCn() );

            } else if (PriceType3.NORMAL.getValue() == purchaseOrder.getPriceType()) {
                // 非含税价格类型的处理，计算不含税单价并设置
                BigDecimal exFactoryPrice = purchaseOrderDetail.getUnitPrice().subtract(warehouseInFee).setScale(2, RoundingMode.HALF_UP);
                purchaseOrderDetail.setExFactoryPrice(exFactoryPrice);
                purchaseOrderDetail.setPurchasePrice(purchaseOrderDetail.getUnitPrice());



            } else if (PriceType3.USD.getValue() == purchaseOrder.getPriceType()) {
                // 美元价格类型的处理，计算不含税单价并设置
                BigDecimal exFactoryPrice = purchaseOrderDetail.getUsdUnitPrice().subtract(warehouseInFee).setScale(2, RoundingMode.HALF_UP);
                purchaseOrderDetail.setExFactoryPrice(exFactoryPrice);
                purchaseOrderDetail.setPurchasePrice(purchaseOrderDetail.getUsdUnitPrice());

                purchaseOrderDetail.setDescription( purchaseOrderDetail.getDescriptionCn() + "/" + purchaseOrderDetail.getDescription() );
            }
        }
        purchaseOrder.setDiscount( discount );
        purchaseOrder.setNotTaxAmountTotal( notTaxAmountTotal );
        purchaseOrder.setTotal( total );
        purchaseOrder.setTotalQty( totalQty );

    }


}
