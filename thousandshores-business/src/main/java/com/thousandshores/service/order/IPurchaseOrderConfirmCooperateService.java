package com.thousandshores.service.order;

import java.util.List;
import java.util.Map;

import com.thousandshores.common.core.domain.AjaxResult;
import com.thousandshores.common.core.domain.model.LoginUser;
import com.thousandshores.domain.order.PurchaseBatchCode;
import com.thousandshores.domain.order.PurchaseOrderConfirmCooperate;
import com.thousandshores.file.domain.OssUploadFileTfg;
import com.thousandshores.system.domain.erpentity.PurchaseOrderDetail;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 采购订单确认Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface IPurchaseOrderConfirmCooperateService 
{
    /**
     * 查询采购订单确认
     * 
     * @param id 采购订单确认主键
     * @return 采购订单确认
     */
    public PurchaseOrderConfirmCooperate selectPurchaseOrderConfirmCooperateById(Long id);

    /**
     * 查询采购订单确认列表
     * 
     * @param purchaseOrderConfirmCooperate 采购订单确认
     * @return 采购订单确认集合
     */
    public List<PurchaseOrderConfirmCooperate> selectPurchaseOrderConfirmCooperateList(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate);

    /**
     * 新增采购订单确认
     * 
     * @param purchaseOrderConfirmCooperate 采购订单确认
     * @return 结果
     */
    public int insertPurchaseOrderConfirmCooperate(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate);

    /**
     * 修改采购订单确认
     * 
     * @param purchaseOrderConfirmCooperate 采购订单确认
     * @return 结果
     */
    public int updatePurchaseOrderConfirmCooperate(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate);

    /**
     * 批量删除采购订单确认
     * 
     * @param ids 需要删除的采购订单确认主键集合
     * @return 结果
     */
    public int deletePurchaseOrderConfirmCooperateByIds(Long[] ids);

    /**
     * 删除采购订单确认信息
     * 
     * @param id 采购订单确认主键
     * @return 结果
     */
    public int deletePurchaseOrderConfirmCooperateById(Long id);

    /**
     * 预览采购订单信息
     * @param id id
     * @return 采购单相关信息
     */
    Map<String, Object> selectPurchaseOrderPreviewById(Long id,LoginUser loginUser);

    /**
     * 导出pdf
     * @param id id
     * @param loginUser 登录用户
     * @param response response
     */
    void exportPdf(Long id, LoginUser loginUser, HttpServletResponse response);

    /**
     * 导出回单
     * @param id id
     * @param response response
     */
    OssUploadFileTfg exportReceipt(Long id, HttpServletResponse response);

    /**
     * 上传回单
     * @param file 文件
     * @param id id
     * @param loginUser loginUser
     */
    void importReceiptData(MultipartFile file, Long id,LoginUser loginUser)throws Exception;

    /**
     * 导出批次码
     * @param idList idList
     * @param loginUser loginUser
     * @param response response
     */
    void exportBatchCode(List<Long> idList, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request);

    /**
     * 导出fnsku码
     * @param detailList detailList
     * @param loginUser loginUser
     * @param response response
     * @param request request
     */
    void exportFnskuCode(List<PurchaseOrderDetail> detailList, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request);

    /**
     * 导出mrp码
     * @param detailList detailList
     * @param loginUser loginUser
     * @param response response
     * @param request request
     */
    void exportMrpCode(List<PurchaseOrderDetail> detailList, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request)throws Exception;

    /**
     * 导出wms码
     * @param detailList detailList
     * @param loginUser loginUser
     * @param response response
     * @param request request
     */
    void exportWmsCode(List<PurchaseOrderDetail> detailList, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request);

    /**
     * 查询批次码数据
     * @param invoice 采购单号
     * @param loginUser loginUser
     * @return List
     */
    List<PurchaseBatchCode> selectPurchaseBatchCodeList(String invoice, LoginUser loginUser);

    /**
     * 查询fnsku数据
     * @param purchaseOrderId 采购单id
     * @param loginUser loginUser
     * @return List
     */
    List<PurchaseOrderDetail> selectPurchaseFnskuList(Long purchaseOrderId, LoginUser loginUser);

    /**
     * 查询mrp数据
     * @param purchaseOrderConfirmCooperate 订单确认
     * @param loginUser loginUser
     * @return List
     */
    List<PurchaseOrderDetail> selectPurchaseMrpList(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate, LoginUser loginUser);

    /**
     * 根据订单确认id查询订单信息
     * @param id id
     * @return PurchaseOrderConfirmCooperate
     */
    PurchaseOrderConfirmCooperate selectPurchaseOrderInfoByConfirmId(Long id);

    /**
     * 查询wms数据
     * @param id  id
     * @param loginUser loginUser
     * @return List
     */
    List<PurchaseOrderDetail> selectPurchaseWmsList(Long id, LoginUser loginUser);

    /**
     * 导出upc码
     * @param itemMappingIdList 明细
     * @param response response
     */
    void exportUpcCode(String itemMappingIdList,  HttpServletResponse response);

    void exportReceiptDownloads(List<Long> ids, LoginUser loginUser, HttpServletResponse response, HttpServletRequest request);
}
