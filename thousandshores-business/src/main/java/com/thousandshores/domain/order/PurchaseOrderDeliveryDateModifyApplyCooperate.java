package com.thousandshores.domain.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.thousandshores.common.annotation.Excel;
import com.thousandshores.common.constant.AppConstant;
import com.thousandshores.common.constant.OrderCooperateConstant;
import com.thousandshores.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * SRM-ERP交期修改记录表对象 tb_purchase_order_delivery_date_modify_cooperate
 * 
 * <AUTHOR>
 * @date 2024-10-12
 */
@EqualsAndHashCode(callSuper = true)
@ToString
@Data
public class PurchaseOrderDeliveryDateModifyApplyCooperate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 供应商ID */
    private Long vendorId;

    /** SRM系统供应商ID */
    private Long supplierId;

    /** 采购单ID */
    private Long purchaseOrderId;

    /** 详情id */
    @Excel(name = "详情id",uploadRequired = true,emptyErrorMessage = "详情id不能为空")
    private Long purchaseOrderDetailId;

    /** 采购单号 */
    @Excel(name = "采购单号",type = Excel.Type.EXPORT)
    private String purchaseInvoice;

    /** sku */
    @Excel(name = "sku",type = Excel.Type.EXPORT)
    private String sku;

    /** 行号 */
    @Excel(name = "行号",type = Excel.Type.EXPORT)
    private String serialNumber;

    /** 行状态 */
    @Excel(name = "行状态", dictType = "purchase_order_delivery_modify_line_status",type = Excel.Type.EXPORT)
    private String lineStatus;

    /** item_id */
    private Long itemId;

    /** 中文关键字 */
    @Excel(name = "中文关键字",type = Excel.Type.EXPORT)
    private String keywordCn;

    /** fnsku */
    @Excel(name = "fnsku",type = Excel.Type.EXPORT)
    private String fnsku;

    /** 采购入库仓id */
    private Long purchaseWarehouseId;

    /** 采购入库仓名称 */
    @Excel(name = "入库仓",type = Excel.Type.EXPORT)
    private String purchaseWarehouseName;

    @Excel(name = "国家代号",type = Excel.Type.EXPORT)
    private String countrySymbol;

    /** 采购单明细类型 */
    @Excel(name = "采购类型",dictType = "purchase_order_detail_type",type = Excel.Type.EXPORT)
    private Integer detailType;

    /** 质检类型 */
    @Excel(name = "质检类型",dictType = "qc_type",type = Excel.Type.EXPORT)
    private Integer detailQcType;

    /** 数据状态,0:待处理,1:已处理 */
    private Integer dataStatus;

    /** 单位 */
    @Excel(name = "单位",type = Excel.Type.EXPORT)
    private String unit;

    /** 超期标识 */
    @Excel(name = "超期标识",type = Excel.Type.EXPORT)
    private String overTimeStatus;

    /** 采购数量 */
    @Excel(name = "采购数量",type = Excel.Type.EXPORT)
    private Integer purchaseQty;

    /** 已收货数量 */
    @Excel(name = "已收数量",type = Excel.Type.EXPORT)
    private Integer alreadyReceiveQty;

    /** 未收货数量 */
    @Excel(name = "未收数量",type = Excel.Type.EXPORT)
    private Integer notReceiveQty;

    /** 已入库数量 */
    @Excel(name = "入库数量",type = Excel.Type.EXPORT)
    private Integer alreadyConfirmedQty;

    /** 初始交期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初始交期", width = 30, dateFormat = "yyyy-MM-dd",type = Excel.Type.EXPORT)
    private Date initialDeliveryDate;

    /** 最新交期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最新交期", width = 30, dateFormat = "yyyy-MM-dd",type = Excel.Type.EXPORT)
    private Date finalDeliveryDate;

    /** 入库日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入库日期", width = 30, dateFormat = "yyyy-MM-dd",type = Excel.Type.EXPORT)
    private Date warehousingDate;

    /** 出厂价 */
    @Excel(name = "出厂价",scale = 2, roundingMode = BigDecimal.ROUND_HALF_UP,type = Excel.Type.EXPORT)
    private BigDecimal factoryPrice ;

    /** 到仓运费 */
    @Excel(name = "运费",scale = 2, roundingMode = BigDecimal.ROUND_HALF_UP,type = Excel.Type.EXPORT)
    private BigDecimal warehouseInFee ;

    /** 总单价 */
    @Excel(name = "总单价",scale = 2, roundingMode = BigDecimal.ROUND_HALF_UP,type = Excel.Type.EXPORT)
    private BigDecimal purchasePrice;

    /** 总价 */
    @Excel(name = "总价",scale = 2, roundingMode = BigDecimal.ROUND_HALF_UP,type = Excel.Type.EXPORT)
    private BigDecimal total ;

    /** 装箱数 */
    @Excel(name = "装箱数")
    private Integer packingQuantity;

    /** 修改类型(枚举类：PurchaseOrderUpdateItem) */
    private Integer updateItem;

    /** 修改原因：具体是那个节点导致(枚举类：PurchaseOrderUpdateRootItem) */
    private Integer updateRootItem;

    /** 影响数量 */
    private Integer impactQty;

    /** 变更前交期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date modifyDeliveryBefore;

    /** 变更后交期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date modifyDeliveryAfter;

    /** 变更状态,0:已拒绝,1:已确认,2:待千岸确认,3:待供应商确认 */
    private Integer modifyStatus;

    /** 发起人 */
    private String promoter;

    /** 发起人邮箱 */
    private String promoterEmail;

    /** 发起时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date promoterTime;

    /** 确认人 */
    private String confirmBy;



    /** 确认人邮箱 */
    private String confirmEmail;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date confirmTime;

    /** excel上传行号 */
    private Integer excelLine;

    /** excel上传错误信息 */
    private List<String> errorList;

    /** 创建人id */
    private Long createById;

    /** 乐观锁版本号 */
    private Date version;

    /** 具体的修改原因(枚举类：PurchaseOrderUpdateChildItem) */
    @Excel(name = "修改原因",dictType = "purchase_order_update_reason_type",uploadRequired = true,
            emptyErrorMessage = OrderCooperateConstant.UPDATE_CHILD_ITEM_ERROR_MESSAGE)
    private Integer updateChildItem;

    @Excel(name = "变更备注"  )
    private String updateRemark;

    /** 交期1 */
    @Excel(name = "交期1",errorMessage = "交期1" + OrderCooperateConstant.UPLOAD_DATE_yyyy_MM_dd_ERROR_MESSAGE,
            dateFormat = AppConstant.YYYY_MM_DD_Slash)
    private Date deliveryDate1;
    /** 数量1 */
    @Excel(name = "数量1",errorMessage = "数量1" + OrderCooperateConstant.INTEGER_GREATER_THAN_ZERO)
    private Integer qty1;
    /** 交期2 */
    @Excel(name = "交期2",errorMessage = "交期2" + OrderCooperateConstant.UPLOAD_DATE_yyyy_MM_dd_ERROR_MESSAGE,
            dateFormat = AppConstant.YYYY_MM_DD_Slash)
    private Date deliveryDate2;
    /** 数量2 */
    @Excel(name = "数量2",errorMessage = "数量2" + OrderCooperateConstant.INTEGER_GREATER_THAN_ZERO)
    private Integer qty2;
    /** 交期3 */
    @Excel(name = "交期3",errorMessage = "交期3" + OrderCooperateConstant.UPLOAD_DATE_yyyy_MM_dd_ERROR_MESSAGE,
            dateFormat = AppConstant.YYYY_MM_DD_Slash)
    private Date deliveryDate3;
    /** 数量3 */
    @Excel(name = "数量3",errorMessage = "数量3" + OrderCooperateConstant.INTEGER_GREATER_THAN_ZERO)
    private Integer qty3;
    /** 交期4 */
    @Excel(name = "交期4",errorMessage = "交期4" + OrderCooperateConstant.UPLOAD_DATE_yyyy_MM_dd_ERROR_MESSAGE,
            dateFormat = AppConstant.YYYY_MM_DD_Slash)
    private Date deliveryDate4;
    /** 数量4 */
    @Excel(name = "数量4",errorMessage = "数量4" + OrderCooperateConstant.INTEGER_GREATER_THAN_ZERO)
    private Integer qty4;
    /** 交期5 */
    @Excel(name = "交期5",errorMessage = "交期5" + OrderCooperateConstant.UPLOAD_DATE_yyyy_MM_dd_ERROR_MESSAGE,
            dateFormat = AppConstant.YYYY_MM_DD_Slash)
    private Date deliveryDate5;
    /** 数量5 */
    @Excel(name = "数量5",errorMessage = "数量5" + OrderCooperateConstant.INTEGER_GREATER_THAN_ZERO)
    private Integer qty5;
    /** 价格查看:YES,NO */
    private String priceCheck;


}
