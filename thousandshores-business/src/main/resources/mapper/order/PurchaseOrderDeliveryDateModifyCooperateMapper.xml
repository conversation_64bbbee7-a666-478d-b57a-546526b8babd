<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thousandshores.mapper.order.PurchaseOrderDeliveryDateModifyCooperateMapper">
    
    <resultMap type="com.thousandshores.domain.order.PurchaseOrderDeliveryDateModifyCooperate" id="PurchaseOrderDeliveryDateModifyCooperateResult">
        <result property="id"    column="id"    />
        <result property="vendorId"    column="vendor_id"    />
        <result property="supplierId"    column="supplier_id"    />
        <result property="purchaseOrderId"    column="purchase_order_id"    />
        <result property="purchaseInvoice"    column="purchase_invoice"    />
        <result property="purchaseOrderDetailId"    column="purchase_order_detail_id"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="itemId"    column="item_id"    />
        <result property="sku"    column="sku"    />
        <result property="keywordCn"    column="keyword_cn"    />
        <result property="fnsku"    column="fnsku"    />
        <result property="purchaseWarehouseId"    column="purchase_warehouse_id"    />
        <result property="purchaseWarehouseName"    column="purchase_warehouse_name"    />
        <result property="detailType"    column="detail_type"    />
        <result property="detailQcType"    column="detail_qc_type"    />
        <result property="dataStatus"    column="data_status"    />
        <result property="overTimeStatus"    column="over_time_status"    />
        <result property="unit"    column="unit"    />
        <result property="purchaseQty"    column="purchase_qty"    />
        <result property="purchasePrice"    column="purchase_price"    />
        <result property="notReceiveQty"    column="not_receive_qty"    />
        <result property="alreadyReceiveQty"    column="already_receive_qty"    />
        <result property="alreadyConfirmedQty"    column="already_confirmed_qty"    />
        <result property="initialDeliveryDate"    column="initial_delivery_date"    />
        <result property="finalDeliveryDate"    column="final_delivery_date"    />
        <result property="warehousingDate"    column="warehousing_date"    />
        <result property="updateItem"    column="update_item"    />
        <result property="updateRootItem"    column="update_root_item"    />
        <result property="updateChildItem"    column="update_child_item"    />
        <result property="impactQty"    column="impact_qty"    />
        <result property="modifyDeliveryBefore"    column="modify_delivery_before"    />
        <result property="modifyDeliveryAfter"    column="modify_delivery_after"    />
        <result property="modifyStatus"    column="modify_status"    />
        <result property="promoter"    column="promoter"    />
        <result property="updateReason"    column="update_reason"    />
        <result property="promoterEmail"    column="promoter_email"    />
        <result property="promoterTime"    column="promoter_time"    />
        <result property="confirmBy"    column="confirm_by"    />
        <result property="confirmEmail"    column="confirm_email"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="createById"    column="create_by_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="version"    column="version"    />
        <result property="remark"    column="remark"    />
        <result property="lineStatus"    column="line_status"    />
        <result property="updateRemark"    column="update_remark"    />
    </resultMap>

    <resultMap type="com.thousandshores.domain.order.PurchaseOrderDeliveryDateModifyApplyCooperate" id="PurchaseOrderDeliveryDateModifyApplyCooperateResult">
        <result property="id"    column="id"    />
        <result property="vendorId"    column="vendor_id"    />
        <result property="supplierId"    column="supplier_id"    />
        <result property="purchaseOrderId"    column="purchase_order_id"    />
        <result property="purchaseInvoice"    column="purchase_invoice"    />
        <result property="purchaseOrderDetailId"    column="purchase_order_detail_id"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="itemId"    column="item_id"    />
        <result property="sku"    column="sku"    />
        <result property="keywordCn"    column="keyword_cn"    />
        <result property="fnsku"    column="fnsku"    />
        <result property="purchaseWarehouseId"    column="purchase_warehouse_id"    />
        <result property="purchaseWarehouseName"    column="purchase_warehouse_name"    />
        <result property="detailType"    column="detail_type"    />
        <result property="detailQcType"    column="detail_qc_type"    />
        <result property="dataStatus"    column="data_status"    />
        <result property="overTimeStatus"    column="over_time_status"    />
        <result property="unit"    column="unit"    />
        <result property="purchaseQty"    column="purchase_qty"    />
        <result property="purchasePrice"    column="purchase_price"    />
        <result property="factoryPrice"    column="factory_price"    />
        <result property="notReceiveQty"    column="not_receive_qty"    />
        <result property="alreadyReceiveQty"    column="already_receive_qty"    />
        <result property="alreadyConfirmedQty"    column="already_confirmed_qty"    />
        <result property="initialDeliveryDate"    column="initial_delivery_date"    />
        <result property="finalDeliveryDate"    column="final_delivery_date"    />
        <result property="warehousingDate"    column="warehousing_date"    />
        <result property="updateItem"    column="update_item"    />
        <result property="updateRootItem"    column="update_root_item"    />
        <result property="updateChildItem"    column="update_child_item"    />
        <result property="impactQty"    column="impact_qty"    />
        <result property="modifyDeliveryBefore"    column="modify_delivery_before"    />
        <result property="modifyDeliveryAfter"    column="modify_delivery_after"    />
        <result property="modifyStatus"    column="modify_status"    />
        <result property="promoter"    column="promoter"    />
        <result property="promoterEmail"    column="promoter_email"    />
        <result property="promoterTime"    column="promoter_time"    />
        <result property="confirmBy"    column="confirm_by"    />
        <result property="confirmEmail"    column="confirm_email"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="createById"    column="create_by_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="version"    column="version"    />
        <result property="remark"    column="remark"    />
        <result property="lineStatus"    column="line_status"    />
        <result property="packingQuantity"    column="packing_quantity"    />
        <result property="priceCheck"    column="price_check"    />
        <result property="countrySymbol"    column="country_symbol"    />
    </resultMap>

    <sql id="selectPurchaseOrderDeliveryDateModifyCooperateVo">
        select id, vendor_id, supplier_id, purchase_order_id, purchase_invoice, purchase_order_detail_id, serial_number, item_id, sku, keyword_cn, fnsku, purchase_warehouse_id, purchase_warehouse_name, detail_type, detail_qc_type, data_status, unit, purchase_qty, not_receive_qty, initial_delivery_date, update_item, update_root_item, update_child_item, impact_qty, modify_delivery_before, modify_delivery_after, modify_status, promoter, promoter_email, promoter_time, confirm_by, confirm_email, confirm_time, create_by_id, create_by, create_time, update_by, update_time, version, remark from e2.tb_purchase_order_delivery_date_modify_cooperate
    </sql>

    <select id="selectPurchaseOrderDeliveryDateModifyCooperateList" parameterType="com.thousandshores.domain.order.PurchaseOrderDeliveryDateModifyCooperate" resultMap="PurchaseOrderDeliveryDateModifyCooperateResult">
        select
            poddmc.*
            ,CASE
            WHEN (pod.qty - ifnull(pod.qty_confirmed,0)) > 0 THEN
            CASE
            WHEN (pod.qty - ifnull(pod.qty_received,0)) = pod.qty THEN 'not_received'
            WHEN (pod.qty - ifnull(pod.qty_received,0)) <![CDATA[  <= ]]>  0 THEN 'fully_received'
            ELSE 'partially_received'
            END
            ELSE 'already_in_stock'
            END AS line_status
        from e2.tb_purchase_order_delivery_date_modify_cooperate poddmc
        left join e2.purchase_order_detail pod on pod.id = poddmc.purchase_order_detail_id
        <where>
            <if test="purchaseInvoice != null  and purchaseInvoice != ''"> and poddmc.purchase_invoice = #{purchaseInvoice}</if>
            <if test="purchaseOrderDetailId != null "> and poddmc.purchase_order_detail_id = #{purchaseOrderDetailId}</if>
            <if test="purchaseOrderId != null "> and poddmc.purchase_order_id = #{purchaseOrderId}</if>
            <if test="itemId != null "> and poddmc.item_id = #{itemId}</if>
            <if test="sku != null  and sku != ''"> and poddmc.sku = #{sku}</if>
            <if test="keywordCn != null  and keywordCn != ''"> and poddmc.keyword_cn = #{keywordCn}</if>
            <if test="fnsku != null  and fnsku != ''"> and poddmc.fnsku = #{fnsku}</if>
            <if test="purchaseWarehouseName != null  and purchaseWarehouseName != ''"> and poddmc.purchase_warehouse_name like concat('%', #{purchaseWarehouseName}, '%')</if>
            <if test="detailType != null "> and poddmc.detail_type = #{detailType}</if>
            <if test="detailQcType != null "> and poddmc.detail_qc_type = #{detailQcType}</if>
            <if test="params.beginInitialDeliveryDate != null and params.beginInitialDeliveryDate != '' and params.endInitialDeliveryDate != null and params.endInitialDeliveryDate != ''"> and poddmc.initial_delivery_date between #{params.beginInitialDeliveryDate} and date_add(#{params.endInitialDeliveryDate}, interval 1 day)</if>
            <if test="params.beginModifyDeliveryBefore != null and params.beginModifyDeliveryBefore != '' and params.endModifyDeliveryBefore != null and params.endModifyDeliveryBefore != ''"> and poddmc.modify_delivery_before between #{params.beginModifyDeliveryBefore} and #{params.endModifyDeliveryBefore}</if>
            <if test="params.beginModifyDeliveryAfter != null and params.beginModifyDeliveryAfter != '' and params.endModifyDeliveryAfter != null and params.endModifyDeliveryAfter != ''"> and poddmc.modify_delivery_after between #{params.beginModifyDeliveryAfter} and #{params.endModifyDeliveryAfter}</if>
            <if test="updateChildItem != null  and updateChildItem != ''"> and poddmc.update_root_item = 3 and poddmc.update_child_item = #{updateChildItem}</if>
            <if test="modifyStatus != null "> and poddmc.modify_status = #{modifyStatus}</if>
            <if test="updateItem != null "> and poddmc.update_item = #{updateItem}</if>
            <if test="dataStatus != null "> and poddmc.data_status = #{dataStatus}</if>
            <if test="modifyDeliveryBefore != null "> and poddmc.modify_delivery_before  like concat('%', #{modifyDeliveryBefore}, '%')</if>
            <if test="modifyDeliveryAfter != null "> and poddmc.modify_delivery_after like concat('%', #{modifyDeliveryAfter}, '%')</if>
        </where>
        having 1 = 1
        <if test="lineStatus != null  and lineStatus != '' "> and line_status = #{lineStatus}</if>
        order by poddmc.id desc
    </select>
    
    <select id="selectPurchaseOrderDeliveryDateModifyCooperateById" parameterType="Long" resultMap="PurchaseOrderDeliveryDateModifyCooperateResult">
        <include refid="selectPurchaseOrderDeliveryDateModifyCooperateVo"/>
        where id = #{id}
    </select>

    <select id="selectPurchaseOrderDeliveryDateModifyApplyCooperateList" parameterType="com.thousandshores.domain.order.PurchaseOrderDeliveryDateModifyApplyCooperate" resultMap="PurchaseOrderDeliveryDateModifyApplyCooperateResult">
        SELECT
        pod.id as  purchase_order_detail_id,
        po.invoice as purchase_invoice,
        po.id as purchase_order_id,
        pod.serial_number,
        pod.item_id,
        im.sku,
        im.keyword_cn,
        im.packing_quantity,
        pm.country_symbol,
        pod.fnsku,
        pod.detail_local_warehouse_id as purchase_warehouse_id,
        sw.`name` as purchase_warehouse_name,
        pod.detail_type,
        (SELECT type FROM e2.purchase_order_delivery_detail podd WHERE podd.purchase_detail_id = pod.id LIMIT 1) as detail_qc_type,
        pod.um as unit,
        ifnull(pod.qty,0) as purchase_qty,
        ifnull(pod.qty_received,0) as already_receive_qty,
        ifnull((pod.qty - pod.qty_received),0) as not_receive_qty,
        ifnull(pod.qty_confirmed,0) as already_confirmed_qty,
        pod.init_delivery_date as initial_delivery_date,
        pod.delivery_date as final_delivery_date,
        pod.warehousing_date,
        CASE
        WHEN (pod.qty - ifnull(pod.qty_received,0)) > 0 THEN
        CASE
        WHEN pod.final_delivery_date IS NULL THEN 'NO'
        WHEN CURRENT_DATE > pod.final_delivery_date THEN 'YES'
        ELSE 'NO'
        END
        ELSE 'NO'
        END AS over_time_status,
        CASE
        WHEN (pod.qty - IFNULL(pod.qty_confirmed,0)) > 0 THEN
        CASE
        WHEN (pod.qty - IFNULL(pod.qty_received,0)) = pod.qty THEN 'not_received'
        WHEN (pod.qty - IFNULL(pod.qty_received,0))   <![CDATA[  <= ]]>  0 THEN 'fully_received'
        ELSE 'partially_received'
        END
        ELSE 'already_in_stock'
        END AS line_status,
        <choose>
            <when test="priceCheck != null  and priceCheck == 'YES'">
                (CASE po.price_type
                WHEN 0 THEN
                pod.tax_unit_price
                WHEN 1 THEN
                pod.unit_price
                WHEN 2 THEN
                pod.usd_unit_price
                END ) as purchase_price,
                ifnull(pod.warehouse_in_fee,0) as warehouse_in_fee,
                ifnull(pod.total,0) as total,
                'YES' as price_check
            </when>
            <otherwise>
                0 as purchase_price,
                0 as warehouse_in_fee,
                0 as total,
                'NO' as price_check
            </otherwise>
        </choose>
        FROM
        e2.purchase_order po
        LEFT JOIN e2.purchase_order_detail pod ON pod.order_id = po.id
        LEFT JOIN e2.vendor_master vm on vm.id = po.vendor_id
        LEFT JOIN e2.storage_warehouse sw on sw.id = pod.detail_local_warehouse_id
        LEFT JOIN e2.platform_master pm on pm.id = pod.platform_id
        LEFT JOIN e2.item_master im ON im.id = pod.item_id
        <where>
            po.`status` IN (3,4,5) AND pod.detail_type IN (0,1,4)  AND po.type = 1 AND IFNULL(po.note,'')  != '虚拟采购单'
            and (pod.qty_confirmed = 0 or pod.qty_confirmed is null) and (pod.qty - ifnull(pod.qty_received,0)) > 0
            <if test="supplierId != null and supplierId != 0">
                and vm.srm_supplier_id = #{supplierId}
            </if>
            <if test="purchaseInvoice != null  and purchaseInvoice != ''"> and po.invoice = #{purchaseInvoice}</if>
            <if test="purchaseOrderDetailId != null "> and pod.id = #{purchaseOrderDetailId}</if>
            <if test="sku != null  and sku != ''"> and im.sku = #{sku}</if>
            <if test="keywordCn != null  and keywordCn != ''"> and im.keyword_cn like concat('%', #{keywordCn}, '%')</if>
            <if test="fnsku != null  and fnsku != ''"> and pod.fnsku = #{fnsku}</if>
            <if test="purchaseWarehouseName != null  and purchaseWarehouseName != ''"> and sw.name like concat('%', #{purchaseWarehouseName}, '%')</if>
            <if test="detailType != null "> and pod.detail_type = #{detailType}</if>
            <if test="params.beginInitialDeliveryDate != null and params.beginInitialDeliveryDate != '' and params.endInitialDeliveryDate != null and params.endInitialDeliveryDate != ''">
                and pod.init_delivery_date between concat(#{params.beginInitialDeliveryDate},' 00:00:00') and concat(#{params.endInitialDeliveryDate},' 23:59:59')
            </if>
            <if test="params.beginFinalDeliveryDate != null and params.beginFinalDeliveryDate != '' and params.endFinalDeliveryDate != null and params.endFinalDeliveryDate != ''">
                and pod.delivery_date between concat(#{params.beginFinalDeliveryDate},' 00:00:00') and concat(#{params.endFinalDeliveryDate},' 23:59:59')
            </if>
            <if test="params.beginWarehousingDate != null and params.beginWarehousingDate != '' and params.endWarehousingDate != null and params.endWarehousingDate != ''">
                and pod.warehousing_date between concat(#{params.beginWarehousingDate},' 00:00:00') and concat(#{params.endWarehousingDate},' 23:59:59')
            </if>
            <if test="updateChildItem != null  and updateChildItem != ''"> and pod.update_child_item = #{updateChildItem}</if>
            <if test="countrySymbol != null  and countrySymbol != ''"> and pm.country_symbol = #{countrySymbol}</if>
            <if test="modifyStatus != null "> and modify_status = #{modifyStatus}</if>
        </where>
        having 1 = 1
            <if test="detailQcType != null "> and detail_qc_type = #{detailQcType}</if>
            <if test="overTimeStatus != null  and overTimeStatus != '' "> and over_time_status = #{overTimeStatus}</if>
            <if test="lineStatus != null  and lineStatus != '' "> and line_status = #{lineStatus}</if>
        order by po.id desc
    </select>

    <select id="selectPurchaseOrderDeliveryDateModifyApplyCooperateByDetailId" parameterType="com.thousandshores.domain.order.PurchaseOrderDeliveryDateModifyApplyCooperate" resultMap="PurchaseOrderDeliveryDateModifyApplyCooperateResult">
        SELECT
        pod.id as  purchase_order_detail_id,
        po.vendor_id,
        vm.srm_supplier_id as supplier_id,
        po.invoice as purchase_invoice,
        po.id as purchase_order_id,
        pod.serial_number,
        pod.item_id,
        im.sku,
        im.keyword_cn,
        pod.fnsku,
        pod.detail_local_warehouse_id as purchase_warehouse_id,
        sw.`name` as purchase_warehouse_name,
        pod.detail_type,
        (SELECT type FROM e2.purchase_order_delivery_detail podd WHERE podd.purchase_detail_id = pod.id LIMIT 1) as detail_qc_type,
        pod.um as unit,
        ifnull(pod.qty,0) as purchase_qty,
        ifnull(pod.qty_received,0) as already_receive_qty,
        ifnull((pod.qty - pod.qty_received),0) as not_receive_qty,
        ifnull(pod.qty_confirmed,0) as already_confirmed_qty,
        pod.init_delivery_date as initial_delivery_date,
        pod.delivery_date as final_delivery_date,
        pod.warehousing_date,
        CASE
        WHEN (pod.qty - pod.qty_received) > 0 THEN
        CASE
        WHEN pod.final_delivery_date IS NULL THEN 'NO'
        WHEN CURRENT_DATE > pod.final_delivery_date THEN 'YES'
        ELSE 'NO'
        END
        ELSE 'NO'
        END AS over_time_status,
        CASE
        WHEN (pod.qty - ifnull(pod.qty_confirmed,0)) > 0 THEN
        CASE
        WHEN (pod.qty - ifnull(pod.qty_received,0)) = pod.qty_received THEN 'not_received'
        WHEN (pod.qty - ifnull(pod.qty_received,0)) <![CDATA[  <= ]]>  0 THEN 'fully_received'
        ELSE 'partially_received'
        END
        ELSE 'already_in_stock'
        END AS line_status,
        (CASE po.price_type
        WHEN 0 THEN
        pod.tax_unit_price
        WHEN 1 THEN
        pod.unit_price
        WHEN 2 THEN
        pod.usd_unit_price
        END ) as purchase_price,
        ifnull(pod.warehouse_in_fee,0) as warehouse_in_fee,
        ifnull(pod.total,0) as total
        FROM
        e2.purchase_order po
        LEFT JOIN e2.purchase_order_detail pod ON pod.order_id = po.id
        LEFT JOIN e2.vendor_master vm on vm.id = po.vendor_id
        LEFT JOIN e2.storage_warehouse sw on sw.id = pod.detail_local_warehouse_id
        LEFT JOIN e2.item_master im ON im.id = pod.item_id
        where po.`status` IN (3,4,5) AND pod.detail_type IN (0,1,4)  AND po.type = 1 AND IFNULL(po.note,'')  != '虚拟采购单'
        <if test="supplierId != null and supplierId != 0">
           and vm.srm_supplier_id = #{supplierId}
        </if>
        and pod.id = #{purchaseOrderDetailId}
        limit 1
    </select>

    <select id="selectPurchaseOrderDeliveryDateModifyCooperateByDetailId" resultMap="PurchaseOrderDeliveryDateModifyCooperateResult">
       select  *
       from e2.tb_purchase_order_delivery_date_modify_cooperate
         where purchase_order_detail_id = #{purchaseOrderDetailId}
        <if test="supplierId != null">
            and supplier_id = #{supplierId}
        </if>
       and data_status = #{dateStatus}  limit 1
    </select>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into e2.tb_purchase_order_delivery_date_modify_cooperate(vendor_id, supplier_id, purchase_order_id, purchase_invoice, purchase_order_detail_id, serial_number, item_id, sku, keyword_cn, fnsku, purchase_warehouse_id, purchase_warehouse_name, detail_type, detail_qc_type, data_status, unit, purchase_qty, not_receive_qty, initial_delivery_date, update_item, update_root_item, update_child_item, impact_qty, modify_delivery_before, modify_delivery_after, modify_status, promoter, promoter_email, promoter_time, confirm_by, confirm_email, confirm_time, create_by_id, create_by, create_time, update_by, update_time, version, remark, update_remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.vendorId}, #{entity.supplierId}, #{entity.purchaseOrderId}, #{entity.purchaseInvoice}, #{entity.purchaseOrderDetailId}, #{entity.serialNumber}, #{entity.itemId}, #{entity.sku}, #{entity.keywordCn}, #{entity.fnsku}, #{entity.purchaseWarehouseId}, #{entity.purchaseWarehouseName}, #{entity.detailType}, #{entity.detailQcType}, #{entity.dataStatus}, #{entity.unit}, #{entity.purchaseQty}, #{entity.notReceiveQty}, #{entity.initialDeliveryDate}, #{entity.updateItem}, #{entity.updateRootItem}, #{entity.updateChildItem}, #{entity.impactQty}, #{entity.modifyDeliveryBefore}, #{entity.modifyDeliveryAfter}, #{entity.modifyStatus}, #{entity.promoter}, #{entity.promoterEmail}, #{entity.promoterTime}, #{entity.confirmBy}, #{entity.confirmEmail}, #{entity.confirmTime}, #{entity.createById}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.version}, #{entity.remark}, #{entity.updateRemark})
        </foreach>
    </insert>

    <insert id="insertPurchaseOrderDeliveryDateModifyCooperate" parameterType="com.thousandshores.domain.order.PurchaseOrderDeliveryDateModifyCooperate" useGeneratedKeys="true" keyProperty="id">
        insert into e2.tb_purchase_order_delivery_date_modify_cooperate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vendorId != null">vendor_id,</if>
            <if test="supplierId != null">supplier_id,</if>
            <if test="purchaseOrderId != null">purchase_order_id,</if>
            <if test="purchaseInvoice != null and purchaseInvoice != ''">purchase_invoice,</if>
            <if test="purchaseOrderDetailId != null">purchase_order_detail_id,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="itemId != null">item_id,</if>
            <if test="sku != null and sku != ''">sku,</if>
            <if test="keywordCn != null">keyword_cn,</if>
            <if test="fnsku != null">fnsku,</if>
            <if test="purchaseWarehouseId != null">purchase_warehouse_id,</if>
            <if test="purchaseWarehouseName != null and purchaseWarehouseName != ''">purchase_warehouse_name,</if>
            <if test="detailType != null">detail_type,</if>
            <if test="detailQcType != null">detail_qc_type,</if>
            <if test="dataStatus != null">data_status,</if>
            <if test="unit != null">unit,</if>
            <if test="purchaseQty != null">purchase_qty,</if>
            <if test="notReceiveQty != null">not_receive_qty,</if>
            <if test="initialDeliveryDate != null">initial_delivery_date,</if>
            <if test="updateItem != null">update_item,</if>
            <if test="updateRootItem != null">update_root_item,</if>
            <if test="updateChildItem != null">update_child_item,</if>
            <if test="impactQty != null">impact_qty,</if>
            <if test="modifyDeliveryBefore != null">modify_delivery_before,</if>
            <if test="modifyDeliveryAfter != null">modify_delivery_after,</if>
            <if test="modifyStatus != null">modify_status,</if>
            <if test="promoter != null">promoter,</if>
            <if test="promoterEmail != null">promoter_email,</if>
            <if test="promoterTime != null">promoter_time,</if>
            <if test="confirmBy != null">confirm_by,</if>
            <if test="confirmEmail != null">confirm_email,</if>
            <if test="confirmTime != null">confirm_time,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="version != null">version,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vendorId != null">#{vendorId},</if>
            <if test="supplierId != null">#{supplierId},</if>
            <if test="purchaseOrderId != null">#{purchaseOrderId},</if>
            <if test="purchaseInvoice != null and purchaseInvoice != ''">#{purchaseInvoice},</if>
            <if test="purchaseOrderDetailId != null">#{purchaseOrderDetailId},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="sku != null and sku != ''">#{sku},</if>
            <if test="keywordCn != null">#{keywordCn},</if>
            <if test="fnsku != null">#{fnsku},</if>
            <if test="purchaseWarehouseId != null">#{purchaseWarehouseId},</if>
            <if test="purchaseWarehouseName != null and purchaseWarehouseName != ''">#{purchaseWarehouseName},</if>
            <if test="detailType != null">#{detailType},</if>
            <if test="detailQcType != null">#{detailQcType},</if>
            <if test="dataStatus != null">#{dataStatus},</if>
            <if test="unit != null">#{unit},</if>
            <if test="purchaseQty != null">#{purchaseQty},</if>
            <if test="notReceiveQty != null">#{notReceiveQty},</if>
            <if test="initialDeliveryDate != null">#{initialDeliveryDate},</if>
            <if test="updateItem != null">#{updateItem},</if>
            <if test="updateRootItem != null">#{updateRootItem},</if>
            <if test="updateChildItem != null">#{updateChildItem},</if>
            <if test="impactQty != null">#{impactQty},</if>
            <if test="modifyDeliveryBefore != null">#{modifyDeliveryBefore},</if>
            <if test="modifyDeliveryAfter != null">#{modifyDeliveryAfter},</if>
            <if test="modifyStatus != null">#{modifyStatus},</if>
            <if test="promoter != null">#{promoter},</if>
            <if test="promoterEmail != null">#{promoterEmail},</if>
            <if test="promoterTime != null">#{promoterTime},</if>
            <if test="confirmBy != null">#{confirmBy},</if>
            <if test="confirmEmail != null">#{confirmEmail},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="createById != null">#{createById},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="version != null">#{version},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePurchaseOrderDeliveryDateModifyCooperate" parameterType="com.thousandshores.domain.order.PurchaseOrderDeliveryDateModifyCooperate">
        update e2.tb_purchase_order_delivery_date_modify_cooperate
        <trim prefix="SET" suffixOverrides=",">
            <if test="vendorId != null">vendor_id = #{vendorId},</if>
            <if test="supplierId != null">supplier_id = #{supplierId},</if>
            <if test="purchaseOrderId != null">purchase_order_id = #{purchaseOrderId},</if>
            <if test="purchaseInvoice != null and purchaseInvoice != ''">purchase_invoice = #{purchaseInvoice},</if>
            <if test="purchaseOrderDetailId != null">purchase_order_detail_id = #{purchaseOrderDetailId},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="sku != null and sku != ''">sku = #{sku},</if>
            <if test="keywordCn != null">keyword_cn = #{keywordCn},</if>
            <if test="fnsku != null">fnsku = #{fnsku},</if>
            <if test="purchaseWarehouseId != null">purchase_warehouse_id = #{purchaseWarehouseId},</if>
            <if test="purchaseWarehouseName != null and purchaseWarehouseName != ''">purchase_warehouse_name = #{purchaseWarehouseName},</if>
            <if test="detailType != null">detail_type = #{detailType},</if>
            <if test="detailQcType != null">detail_qc_type = #{detailQcType},</if>
            <if test="dataStatus != null">data_status = #{dataStatus},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="purchaseQty != null">purchase_qty = #{purchaseQty},</if>
            <if test="notReceiveQty != null">not_receive_qty = #{notReceiveQty},</if>
            <if test="initialDeliveryDate != null">initial_delivery_date = #{initialDeliveryDate},</if>
            <if test="updateItem != null">update_item = #{updateItem},</if>
            <if test="updateRootItem != null">update_root_item = #{updateRootItem},</if>
            <if test="updateChildItem != null">update_child_item = #{updateChildItem},</if>
            <if test="impactQty != null">impact_qty = #{impactQty},</if>
            <if test="modifyDeliveryBefore != null">modify_delivery_before = #{modifyDeliveryBefore},</if>
            <if test="modifyDeliveryAfter != null">modify_delivery_after = #{modifyDeliveryAfter},</if>
            <if test="modifyStatus != null">modify_status = #{modifyStatus},</if>
            <if test="promoter != null">promoter = #{promoter},</if>
            <if test="promoterEmail != null">promoter_email = #{promoterEmail},</if>
            <if test="promoterTime != null">promoter_time = #{promoterTime},</if>
            <if test="confirmBy != null">confirm_by = #{confirmBy},</if>
            <if test="confirmEmail != null">confirm_email = #{confirmEmail},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="version != null">version = #{version},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="confirmPurchaseOrderDeliveryDateModifyCooperate">
        update e2.tb_purchase_order_delivery_date_modify_cooperate
        set modify_status = #{modifyStatus},data_status = #{dataStatus},confirm_by= #{confirmBy},confirm_email= #{confirmEmail},confirm_time= #{confirmTime},version = now()
        where id = #{id}
    </update>

    <delete id="deletePurchaseOrderDeliveryDateModifyCooperateById" parameterType="Long">
        delete from e2.tb_purchase_order_delivery_date_modify_cooperate where id = #{id}
    </delete>

    <delete id="deletePurchaseOrderDeliveryDateModifyCooperateByIds" parameterType="String">
        delete from e2.tb_purchase_order_delivery_date_modify_cooperate where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>