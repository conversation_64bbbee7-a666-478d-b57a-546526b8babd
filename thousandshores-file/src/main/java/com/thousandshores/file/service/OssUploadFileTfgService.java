package com.thousandshores.file.service;


import com.thousandshores.common.core.domain.entity.SysUser;
import com.thousandshores.common.enums.file.FileBusinessEnum;
import com.thousandshores.file.domain.OssUploadFile;
import com.thousandshores.file.domain.OssUploadFileTfg;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.zip.ZipOutputStream;

/**
 * OSS上传文件关联关系表(供应链相关)(OssUploadFileTfg)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-08 17:15:50
 */
public interface OssUploadFileTfgService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    OssUploadFileTfg queryById(Long id);

    /**
     * 分页查询
     *
     * @param ossUploadFileTfg 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<OssUploadFileTfg> queryByPage(OssUploadFileTfg ossUploadFileTfg, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param ossUploadFileTfg 实例对象
     * @return 实例对象
     */
    OssUploadFileTfg insert(OssUploadFileTfg ossUploadFileTfg);

    /**
     * 修改数据
     *
     * @param ossUploadFileTfg 实例对象
     * @return 实例对象
     */
    OssUploadFileTfg update(OssUploadFileTfg ossUploadFileTfg);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 根据业务id和类型查询文件信息
     * @param relationId 业务id
     * @param businessType 业务类型
     * @return OssUploadFileTfg
     */
    OssUploadFileTfg queryOneByRelationId(Long relationId,String businessType);

    /**
     * 上传文件
     * @param multipartFile 文件
     * @param relationId 关联id
     * @param relationInvoice 关联单号
     * @param fileBusinessEnum 文件枚举
     * @param customFileName 自定义文件名
     * @throws Exception 异常抛出
     */
    OssUploadFileTfg uploadFile(MultipartFile multipartFile, Long relationId, String relationInvoice,
                                FileBusinessEnum fileBusinessEnum, SysUser user, String remark, String customFileName)throws Exception;

    /**
     * 上传文件
     * @param bytes 文件字节数组
     * @param relationId 关联id
     * @param relationInvoice 关联单号
     * @param fileBusinessEnum 文件枚举
     * @param user 用户
     * @param remark 备注
     * @param originalFilename 文件名
     * @param filePostFix 文件后缀
     * @return OssUploadFileTfg
     * @throws Exception 异常抛出
     */
    OssUploadFileTfg uploadFile(byte[] bytes, Long relationId, String relationInvoice, FileBusinessEnum fileBusinessEnum
            , SysUser user, String remark, String originalFilename, String filePostFix)throws Exception;

    /**
     * 下载upc文件
     * @param itemMappingIdList 文件id集合
     * @param businessCode 业务编码
     * @param zipOutputStream 压缩流
     */
    void downLoadUpcFile(List<Long> itemMappingIdList, String businessCode, ZipOutputStream zipOutputStream);

    void downloadZip(List<Long> ids, String businessCode, ZipOutputStream zipOutputStream);
}
