package com.thousandshores.file.service.impl;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson2.JSON;
import com.shores.file.entity.TbSystemFileStorage;
import com.shores.file.service.DubboFileService;
import com.thousandshores.common.constant.AppConstant;
import com.thousandshores.common.core.domain.entity.SysUser;
import com.thousandshores.common.enums.file.FileBusinessEnum;
import com.thousandshores.common.enums.file.FileStorageSite;
import com.thousandshores.common.exception.ServiceException;
import com.thousandshores.common.utils.MyAssert;
import com.thousandshores.common.utils.StringUtils;
import com.thousandshores.file.domain.OssUploadFileTfg;
import com.thousandshores.file.mapper.OssUploadFileTfgMapper;
import com.thousandshores.file.service.OssUploadFileTfgService;
import org.omg.CORBA.SystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * OSS上传文件关联关系表(供应链相关)(OssUploadFileTfg)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-08 17:15:53
 */
@Service("ossUploadFileTfgService")
public class OssUploadFileTfgServiceImpl implements OssUploadFileTfgService {


    private static final int BUFFER_SIZE = 4096;

    private final Logger logs = LoggerFactory.getLogger( this.getClass() );

    @Resource
    private OssUploadFileTfgMapper ossUploadFileTfgDao;

    @Reference
    private DubboFileService dubboFileService;


    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public OssUploadFileTfg queryById(Long id) {
        return this.ossUploadFileTfgDao.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param ossUploadFileTfg 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @Override
    public Page<OssUploadFileTfg> queryByPage(OssUploadFileTfg ossUploadFileTfg, PageRequest pageRequest) {
        long total = this.ossUploadFileTfgDao.count(ossUploadFileTfg);
        return new PageImpl<>(this.ossUploadFileTfgDao.queryAllByLimit(ossUploadFileTfg, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param ossUploadFileTfg 实例对象
     * @return 实例对象
     */
    @Override
    public OssUploadFileTfg insert(OssUploadFileTfg ossUploadFileTfg) {
        this.ossUploadFileTfgDao.insert(ossUploadFileTfg);
        return ossUploadFileTfg;
    }

    /**
     * 修改数据
     *
     * @param ossUploadFileTfg 实例对象
     * @return 实例对象
     */
    @Override
    public OssUploadFileTfg update(OssUploadFileTfg ossUploadFileTfg) {
        this.ossUploadFileTfgDao.update(ossUploadFileTfg);
        return this.queryById(ossUploadFileTfg.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.ossUploadFileTfgDao.deleteById(id) > 0;
    }

    @Override
    public OssUploadFileTfg queryOneByRelationId(Long relationId, String businessType) {
        return ossUploadFileTfgDao.queryOneByRelationId(relationId, businessType);
    }

    public OssUploadFileTfg initOssUploadFileTfg(String originalFilename, Long relationId, String relationInvoice
            , FileBusinessEnum fileBusinessEnum, TbSystemFileStorage tbSystemFileStorage, SysUser user, String remark){
        OssUploadFileTfg OssUploadFile = new OssUploadFileTfg();
        OssUploadFile.setFileType(fileBusinessEnum.getFileCode());
        OssUploadFile.setFileTypeName(fileBusinessEnum.getDesc());
        OssUploadFile.setBusinessType(fileBusinessEnum.getBusinessCode());
        OssUploadFile.setRelationBusinessId(relationId);
        OssUploadFile.setRelationInvoice(relationInvoice);
        OssUploadFile.setOssId(tbSystemFileStorage.getId());
        OssUploadFile.setFileName(originalFilename);
        OssUploadFile.setFileSuffix(tbSystemFileStorage.getFilePostfix());
        OssUploadFile.setFileSize(tbSystemFileStorage.getFileSize());
        OssUploadFile.setCreatedBy(user.getUserId());
        OssUploadFile.setCreatedName(user.getUserName());
        OssUploadFile.setCreatedDate(new Date() );
        OssUploadFile.setRemark(remark);
        return OssUploadFile;
    }

    /**
     * 上传文件方法
     * 该方法用于将用户上传的文件存储到文件系统中，并在业务数据库中记录文件的相关信息及关联关系
     * 该方法是事务性的，如果出现异常，事务将被回滚
     *
     * @param multipartFile 待上传的文件，类型为MultipartFile
     * @param relationId 关联ID，用于标识文件与业务实体的关联
     * @param relationInvoice 关联单号，用于在某些业务场景下标识文件关联的单号
     * @param fileBusinessEnum 文件业务类型枚举，用于指定文件上传的业务场景
     * @param user 操作用户对象，用于记录操作员信息
     * @param remark 备注信息，用于记录文件上传的额外说明
     * @param customFileName 自定义文件名
     * @throws Exception 如果方法执行过程中出现任何异常，将抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OssUploadFileTfg uploadFile(MultipartFile multipartFile, Long relationId, String relationInvoice, FileBusinessEnum fileBusinessEnum, SysUser user, String remark, String customFileName)throws Exception {
        // 获取文件字节内容
        byte[] bytes = multipartFile.getBytes();
        //确定文件名
        String fileName = StringUtils.isBlank( customFileName ) ? multipartFile.getOriginalFilename() : customFileName;
        // 调用远程文件存储服务，上传文件
        TbSystemFileStorage tbSystemFileStorage = dubboFileService.uploadFile(bytes, fileName, multipartFile.getContentType(), fileBusinessEnum.getFileCode(), fileBusinessEnum.getDesc(), FileStorageSite.OSS.getType());
        // 判断文件上传结果，如果成功则进一步处理
        if(null != tbSystemFileStorage && AppConstant.OSS_UPLOAD_FILE_STORAGE_CODE.equals(tbSystemFileStorage.getResult().getAppCode())){
            // 初始化文件关联对象，用于保存到业务数据库中
            OssUploadFileTfg OssUploadFile = initOssUploadFileTfg( fileName, relationId, relationInvoice, fileBusinessEnum, tbSystemFileStorage, user, remark );
            // 插入文件关联信息到数据库
            this.insert( OssUploadFile );
            return OssUploadFile;
        }
        return null;
    }

    /**
     * 上传文件到对象存储OSS
     *
     * @param bytes 文件的字节内容
     * @param relationId 关联的业务ID
     * @param relationInvoice 关联的发票号码
     * @param fileBusinessEnum 文件业务类型枚举
     * @param user 当前用户信息
     * @param remark 备注信息
     * @param originalFilename 文件原始名称
     * @param filePostFix 文件后缀名
     * @return OssUploadFileTfg 文件上传对象实例
     * @throws Exception 抛出异常，上传过程中发生错误时回滚事务
     * 该方法主要负责将字节数据上传到对象存储OSS，同时在数据库中记录文件的相关信息
     * 它首先调用远程文件存储服务进行文件上传，然后根据上传结果决定是否继续在业务数据库中保存文件的元数据信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public OssUploadFileTfg uploadFile(byte[] bytes, Long relationId, String relationInvoice, FileBusinessEnum fileBusinessEnum
            , SysUser user, String remark, String originalFilename, String filePostFix)throws Exception {
        // 调用远程文件存储服务，上传文件
        TbSystemFileStorage tbSystemFileStorage = dubboFileService.uploadFile(bytes, originalFilename, filePostFix, fileBusinessEnum.getFileCode(), fileBusinessEnum.getDesc(), FileStorageSite.OSS.getType());

        // 判断文件上传结果，如果成功则进一步处理
        if(null != tbSystemFileStorage && AppConstant.OSS_UPLOAD_FILE_STORAGE_CODE.equals(tbSystemFileStorage.getResult().getAppCode())){
            // 初始化文件关联对象，用于保存到业务数据库中
            OssUploadFileTfg OssUploadFile = initOssUploadFileTfg( originalFilename, relationId, relationInvoice, fileBusinessEnum, tbSystemFileStorage, user, remark );
            // 插入文件关联信息到数据库
            this.insert( OssUploadFile );
            return OssUploadFile;
        }else {
            logs.error( "文件上传失败,{}", JSON.toJSONString( tbSystemFileStorage ) );
        }
        return null;
    }

    @Override
    public void downLoadUpcFile(List<Long> itemMappingIdList, String businessCode, ZipOutputStream zipOutputStream) {
        int flag = 0;
        for (Long id : itemMappingIdList) {
            OssUploadFileTfg ossUploadFileTfg = ossUploadFileTfgDao.queryOneByRelationId( id, businessCode );
            if (ossUploadFileTfg != null && ossUploadFileTfg.getOssId() != null){
                this.zipFile(zipOutputStream, ossUploadFileTfg.getOssId());
                flag ++;
            }else {
                logs.warn( "下载文件异常,relationBusinessId:{},businessType:{}", id,businessCode );
            }
        }
        if (flag == 0){
            throw new ServiceException("产品映射中无对应的UPC条码文件");
        }
    }

    @Override
    public void downloadZip(List<Long> ids, String businessCode, ZipOutputStream zipOutputStream) {
        MyAssert.isEmptyList( ids, "参数不能为空" );
        int flag = 0;
        for (Long id : ids) {
            OssUploadFileTfg ossUploadFileTfg = ossUploadFileTfgDao.queryOneByRelationId( id, businessCode );
            if (ossUploadFileTfg != null && ossUploadFileTfg.getOssId() != null){
                this.zipFile(zipOutputStream, ossUploadFileTfg.getOssId());
                flag ++;
            }else {
                logs.warn( "下载文件异常,relationBusinessId:{},businessType:{}", id,businessCode );
            }
        }
        if (flag == 0){
            throw new ServiceException("没有可下载的文件");
        }
    }

    public void zipFile(ZipOutputStream zipOutputStream, Long fileStorageId) {
        String urlString = dubboFileService.generatorOuterLink(fileStorageId);

        // 解析出URL中的文件名
        String[] split = urlString.split("\\?");
        String url = split[0];
        String[] split1 = url.split("/");
        // 防止文件名一致出错 java.util.zip.ZipException: duplicate entry: ffmpeg.dll
        String fileName = System.currentTimeMillis() + "_" + split1[split1.length - 1];

        try (BufferedInputStream bis = new BufferedInputStream(new URL(urlString).openStream())) {
            fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8.toString());
            // 创建url对象
            URL urlObj = new URL(urlString);
            // 创建HttpURLConnection对象，通过这个对象打开跟远程服务器之间的连接
            HttpURLConnection httpConn = (HttpURLConnection) urlObj.openConnection();
            httpConn.setDoInput(true);
            httpConn.setRequestMethod("GET");
            httpConn.setConnectTimeout(5000);

            // HttpURLConnection 不需要在 try-with-resources 中声明，因为它没有 close 方法
            if (httpConn.getResponseCode() == 200) {
                ZipEntry zipEntryXtv = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntryXtv);

                byte[] buffer = new byte[BUFFER_SIZE];
                int length;
                while ((length = bis.read(buffer)) > 0) {
                    zipOutputStream.write(buffer, 0, length);
                }

                zipOutputStream.closeEntry();
            } else {
                throw new IOException("下载文件失败: HTTP response code " + httpConn.getResponseCode());
            }
        } catch (IOException e) {
            // 更好地处理异常，例如记录日志
            logs.error("压缩文件异常", e);
        }
    }

}
