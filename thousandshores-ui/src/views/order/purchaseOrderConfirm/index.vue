<style>
.el-row {
  margin-bottom: 20px;
}
.el-row:last-child {
  margin-bottom: 0;
}
.el-col {
  border-radius: 4px;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}
</style>

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="采购单号" prop="purchaseInvoice">
        <el-input
          v-model="queryParams.purchaseInvoice"
          placeholder="请输入采购单号"
          style="width: 240px"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="回签状态" prop="dataStatus">
        <el-select style="width: 240px" v-model="queryParams.dataStatus" placeholder="请选择回签状态" clearable>
          <el-option
            v-for="dict in tb_purchase_order_confirm_data_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="sku" prop="sku">
        <el-input
            style="width: 240px"
            v-model="queryParams.sku"
            placeholder="请输入sku"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="中文关键字" prop="keywordCn">
        <el-input
            style="width: 240px"
            v-model="queryParams.keywordCn"
            placeholder="请输入中文关键字"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="单据状态" prop="purchaseOrderStatus">
        <el-select style="width: 240px" v-model="queryParams.purchaseOrderStatus" placeholder="请选择单据状态" clearable>
          <el-option
            v-for="dict in tb_purchase_order_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="付款状态" prop="paymentStatus">
        <el-select style="width: 240px" v-model="queryParams.paymentStatus" placeholder="请选择付款状态" clearable>
          <el-option
            v-for="dict in tb_purchase_order_payment_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>


      <el-form-item label="订单通知时间" style="width: 340px">
        <el-date-picker
          v-model="daterangePurchaseOrderNoticeTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="回签人" prop="counterSignatureBy">
        <el-input
          v-model="queryParams.counterSignatureBy"
          placeholder="请输入回签人"
          style="width: 240px"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="回签时间" style="width: 340px">
        <el-date-picker
          v-model="daterangeCounterSignatureTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="审核通过时间" style="width: 340px">
        <el-date-picker
            v-model="daterangeReviewTime"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="创建时间" style="width: 340px">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Download"
          :disabled="multiple"
          @click="handleDownload"
        >批量下载</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Search"  @click="handleQuery">搜索</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-col>
    </el-row>


    <el-tabs type="border-card" v-model="activeName" @tab-click="handleTabClick" >
      <el-tab-pane label="待回签"  name="waitConfirm" >
        <el-table v-loading="loading" :data="purchaseOrderConfirmList" @selection-change="handleSelectionChange">
          <el-table-column type="selection"  width="55" align="center" fixed="left" />
          <el-table-column label="审核通过时间" align="center" prop="reviewTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.reviewTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="采购单号" align="center" prop="purchaseInvoice" />
          <el-table-column label="回签发起人" align="center" prop="purchaseCounterSignatureUserName" />
          <el-table-column label="采购数量" align="center" prop="purchaseQty" />
          <el-table-column label="币种" align="center" prop="currency" />
          <el-table-column label="金额" align="center">
            <template #default="scope">
              {{ scope.row.amount.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="数据状态" align="center" prop="dataStatus">
            <template #default="scope">
              <dict-tag :options="tb_purchase_order_confirm_data_status" :value="scope.row.dataStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="单据状态" align="center" prop="purchaseOrderStatus">
            <template #default="scope">
              <dict-tag :options="tb_purchase_order_status" :value="scope.row.purchaseOrderStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="付款状态" align="center" prop="paymentStatus">
            <template #default="scope">
              <dict-tag :options="tb_purchase_order_payment_status" :value="scope.row.paymentStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="版本号" align="center" prop="invoice" />
          <el-table-column label="订单通知时间" align="center" prop="purchaseOrderNoticeTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.purchaseOrderNoticeTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" header-align="center" class-name="small-padding fixed-width" width="300" >
            <template #default="scope">
              <el-button type="primary"  @click="handlePreview(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-left:12px;margin-top: 5px;float: left" icon="View" >
                预览
              </el-button>
              <el-button type="primary" @click="downloadReceipt(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                下载
              </el-button>
              <el-button type="primary"   @click="downloadBatchCode(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                批次码
              </el-button>
              <el-button type="primary"  @click="downloadFnsku(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                FNSKU条码
              </el-button>
              <el-button type="primary" v-if="scope.row.targetCountryCode != null && scope.row.targetCountryCode == 'IN'"  @click="downloadMrp(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                印度MRP标
              </el-button>
              <el-button type="primary"  @click="downloadWms(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                WMS二维码
              </el-button>
              <el-button type="primary"  @click="downloadUpc(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                upc文件
              </el-button>
              <el-button
                  type="warning"
                  icon="Upload"
                  style="margin-top: 5px;float: left"
                  @click="handleImport(scope.row)"
                  v-hasPermi="['order:purchaseOrderConfirm:edit']"
              >上传</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />
      </el-tab-pane>

      <el-tab-pane label="已回签" name="confirmed" >
        <el-table v-loading="loading" :data="purchaseOrderConfirmList">
          <el-table-column label="审核通过时间" align="center" prop="reviewTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.reviewTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="采购单号" align="center" prop="purchaseInvoice" />
          <el-table-column label="回签发起人" align="center" prop="purchaseCounterSignatureUserName" />
          <el-table-column label="采购数量" align="center" prop="purchaseQty" />
          <el-table-column label="币种" align="center" prop="currency" />
          <el-table-column label="金额" align="center">
            <template #default="scope">
              {{ scope.row.amount.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="数据状态" align="center" prop="dataStatus">
            <template #default="scope">
              <dict-tag :options="tb_purchase_order_confirm_data_status" :value="scope.row.dataStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="单据状态" align="center" prop="purchaseOrderStatus">
            <template #default="scope">
              <dict-tag :options="tb_purchase_order_status" :value="scope.row.purchaseOrderStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="付款状态" align="center" prop="paymentStatus">
            <template #default="scope">
              <dict-tag :options="tb_purchase_order_payment_status" :value="scope.row.paymentStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="版本号" align="center" prop="invoice" />
          <el-table-column label="订单通知时间" align="center" prop="purchaseOrderNoticeTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.purchaseOrderNoticeTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="回签人" align="center" prop="counterSignatureBy" />
          <el-table-column label="回签时间" align="center" prop="counterSignatureTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.counterSignatureTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" header-align="center" class-name="small-padding fixed-width" width="300" >
            <template #default="scope">
              <el-button type="primary"  @click="handlePreview(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-left:12px;margin-top: 5px;float: left" icon="View" >
                预览
              </el-button>
              <el-button type="primary" @click="downloadReceipt(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                下载
              </el-button>
              <el-button type="primary"   @click="downloadBatchCode(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                批次码
              </el-button>
              <el-button type="primary"  @click="downloadFnsku(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                FNSKU条码
              </el-button>
              <el-button type="primary" v-if="scope.row.targetCountryCode != null && scope.row.targetCountryCode == 'IN'" @click="downloadMrp(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                印度MRP标
              </el-button>
              <el-button type="primary"  @click="downloadWms(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                WMS二维码
              </el-button>
              <el-button type="primary"  @click="downloadUpc(scope.row)" v-hasPermi="['order:purchaseOrderConfirm:query']" style="margin-top: 5px;float: left" icon="Download" >
                upc文件
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="total>0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>



    <!-- 预览对话框 -->
    <el-dialog   :title="title" v-model="open" width="70%" append-to-body>
      <div class="po-container">
        <el-row  >
          <el-col :span="8">
            <table>
              <tr>
                <td  style="width: 90px; padding-top: 0;padding-bottom: 0;text-align: justify;" rowspan="2">
                  <img style="width: 90px" v-if="companyIcon" :src="companyIcon"  />
                </td>
                <td class="custom-td">
                  <strong style="font-size: 10px;" >{{previewData.purchaseOrder.companyNameEn }}</strong><br/>
                  <strong class="tight-text" style="font-size: 14px;">{{previewData.purchaseOrder.companyName }}</strong>
                </td>
              </tr>
            </table>
          </el-col>
          <el-col :span="8"></el-col>
          <el-col :span="8">
            <img style="float: right;width:100%;height: 70%" :src="imageUrl" width="100%"/>
          </el-col>
        </el-row>

        <h2 style="text-align: center" class="po-title"><strong>采&nbsp购&nbsp订&nbsp单</strong></h2>
        <el-row :gutter="20" >
          <el-col :span="16">订单号: {{previewData.purchaseOrder.invoice }} </el-col>
          <el-col :span="8">复审日期:{{ parseTime(previewData.purchaseOrder.reviewTime, '{y}-{m}-{d}') }}</el-col>

          <el-col :span="16">供应商：{{previewData.purchaseOrder.vendorName }}</el-col>
          <el-col :span="8">出厂代号：{{previewData.purchaseOrder.invoice.split("-")[1] }}</el-col>

          <el-col :span="16">供方地址：{{previewData.purchaseOrder.vendorLocation }}</el-col>
          <el-col :span="8">结款方式：{{previewData.purchaseOrder.vendorPaymentMethod }}</el-col>

          <el-col :span="16">供方联系方式：{{previewData.purchaseOrder.vendorContact }} TEL：{{previewData.purchaseOrder.vendorTel }} FAX：{{previewData.purchaseOrder.vendorFax }}</el-col>
          <el-col :span="8">目的仓国家：{{previewData.targetWarehouseCountrySymbol }}</el-col>
        </el-row>
        <el-table :data="purchaseOrderDetailList"  v-if="priceType == 0" style="width: 100%">
          <el-table-column label="序号">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="sku" label="产品编号" width="100"  />
          <el-table-column prop="description" :show-overflow-tooltip="true" label="名称/规格描述" width="100" />
          <el-table-column prop="um" label="单位" width="40" />
          <el-table-column prop="qty" label="数量"  />
          <el-table-column prop="exFactoryPrice" label="出厂价(含税)"  >
            <template #default="scope">
              {{ previewData.currencySymbol }} {{ scope.row.exFactoryPrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="warehouseInFee" label="国内到仓运费"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.warehouseInFee.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="purchasePrice" label="含税单价"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.purchasePrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="total" label="含税金额"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.total.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="taxRate" width="60" label="税率"  >
            <template #default="scope">
              {{ scope.row.taxRate }}%
            </template>
          </el-table-column>>
          <el-table-column prop="taxAmount" label="税额"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.taxAmount.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="excludeTaxAmount" label="不含税金额"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.excludeTaxAmount.toFixed(2)}}
            </template>
          </el-table-column>
          <el-table-column prop="detailLocalWareName" label="采购仓库"  />
          <el-table-column prop="deliveryDate" label="交期"  >
            <template #default="scope">
              <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table :data="purchaseOrderDetailList" v-if="priceType == 1" style="width: 100%">
          <el-table-column label="序号">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="sku" label="产品编号" width="150"  />
          <el-table-column prop="description" :show-overflow-tooltip="true" label="名称/规格描述" width="180" />
          <el-table-column prop="um" label="单位"  />
          <el-table-column prop="qty" label="数量"  />
          <el-table-column prop="exFactoryPrice" label="出厂价(不含税)"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.exFactoryPrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="warehouseInFee" label="国内到仓运费"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.warehouseInFee.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="purchasePrice" label="总单价"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.purchasePrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="total" label="金额"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.total.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="detailLocalWareName" label="采购仓库"  />
          <el-table-column prop="deliveryDate" label="交期"  >
            <template #default="scope">
              <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table :data="purchaseOrderDetailList" v-if="priceType == 2" style="width: 100%">
          <el-table-column label="序号">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="sku" label="产品编号" width="150"  />
          <el-table-column prop="description" :show-overflow-tooltip="true" label="名称/规格描述" width="180" />
          <el-table-column prop="um" label="单位"  />
          <el-table-column prop="qty" label="数量"  />
          <el-table-column prop="exFactoryPrice" label="出厂价(美元)"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.exFactoryPrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="warehouseInFee" label="国内到仓运费"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.warehouseInFee.toFixed(2)}}
            </template>
          </el-table-column>
          <el-table-column prop="purchasePrice" label="含税单价"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.purchasePrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="total" label="金额"  >
            <template #default="scope">
              {{ previewData.currencySymbol }}{{ scope.row.total.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="detailLocalWareName" label="采购仓库"  />
          <el-table-column prop="deliveryDate" label="交期"  >
            <template #default="scope">
              <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- 合计显示-->
        <el-row v-if="priceType == 0" >
          <el-col :span="7"></el-col>
          <el-col :span="3">总数量: {{previewData.purchaseOrder.totalQty }} </el-col>
          <el-col :span="4">折扣: {{previewData.purchaseOrder.discount.toFixed(2) }} </el-col>
          <el-col :span="4">总金额:{{ previewData.currencySymbol }}{{previewData.purchaseOrder.total.toFixed(2) }}</el-col>
          <el-col :span="4">不含税总金额:{{ previewData.currencySymbol }}{{previewData.purchaseOrder.notTaxAmountTotal.toFixed(2) }}</el-col>
          <el-col :span="2"></el-col>
        </el-row>
        <el-row v-if="priceType == 1" >
          <el-col :span="10"></el-col>
          <el-col :span="4">总数量: {{previewData.purchaseOrder.totalQty }} </el-col>
          <el-col :span="4">折扣: {{ previewData.currencySymbol }}{{previewData.purchaseOrder.discount.toFixed(2) }} </el-col>
          <el-col :span="4">总金额:{{ previewData.currencySymbol }}{{previewData.purchaseOrder.total.toFixed(2) }}</el-col>
          <el-col :span="2"></el-col>
        </el-row>
        <el-row v-if="priceType == 2" >
          <el-col :span="10"></el-col>
          <el-col :span="4">总数量: {{previewData.purchaseOrder.totalQty }} </el-col>
          <el-col :span="4">折扣: {{ previewData.currencySymbol }}{{previewData.purchaseOrder.discount.toFixed(2) }} </el-col>
          <el-col :span="4">总金额:{{ previewData.currencySymbol }}{{previewData.purchaseOrder.total.toFixed(2) }}</el-col>
          <el-col :span="2"></el-col>
        </el-row>


        <el-row>
          <el-col :span="24"><strong style="font-size: medium" >说明：</strong></el-col>
          <ol>
            <li>
              <el-col :span="24">千岸深圳仓库收货地址: {{previewData.szWarehouseContact.receivingAddress }} </el-col>
              <el-col :span="24">仓库收货联系方式 ：{{previewData.szWarehouseContact.contact }} </el-col>
            </li>
            <li>
              <el-col :span="24">千岸深圳仓库收货地址: {{previewData.ywWarehouseContact.receivingAddress }} </el-col>
              <el-col :span="24">仓库收货联系方式 ：{{previewData.ywWarehouseContact.contact }} </el-col>
            </li>
            <li>其他未尽事项请参考双方签订的《采购合作合同》和《千岸供应商指引》。</li>
          </ol>
          <el-col :span="24"><strong style="font-size: medium" >备注：</strong></el-col>
          <el-col :span="24">{{previewData.purchaseOrder.note }} </el-col>
          <el-col :span="4"></el-col>
          <el-col :span="4"><strong style="font-size: medium">PO确认人：{{previewData.purchaseOrder.confirmUserName }}</strong> </el-col>
          <el-col :span="4"><strong style="font-size: medium">审核：{{previewData.approveUser }}</strong> </el-col>
          <el-col :span="4"><strong style="font-size: medium">批准：{{previewData.reviewUser }}</strong> </el-col>
          <el-col :span="4"><strong style="font-size: medium">供应商回签：</strong> </el-col>
          <el-col :span="4"></el-col>
          <el-col :span="24">&nbsp&nbsp</el-col>
          <el-col :span="24" style="text-align: center" >深圳办公室地址：{{previewData.szWarehouseContact.officeAddress}}  电话: {{previewData.szWarehouseContact.officeTelephone}}  传真：{{previewData.szWarehouseContact.officeFax}}</el-col>
          <el-col :span="24" style="text-align: center" >义乌办公室地址：{{previewData.ywWarehouseContact.officeAddress}}  电话: {{previewData.ywWarehouseContact.officeTelephone}}  传真：{{previewData.ywWarehouseContact.officeFax}}</el-col>
        </el-row>

      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="printPDF()">PDF</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 回单上传 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
          ref="uploadRef"
          :limit="1"
          :headers="upload.headers"
          :action="upload.url + '?id=' + upload.uploadId"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm()">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 批次码下载 -->
    <el-dialog :title="downloadBatchcodeTitle" v-model="downloadBatchcodeOpen" width="800px" append-to-body>
        <el-table :data="downloadBatchCodeList" @selection-change="handleDownloadBatchCodeSelectionChange"  >
          <el-table-column type="selection" width="55" align="center"  />
          <el-table-column prop="batchCode" label="批次码"  align="center" />
          <el-table-column prop="sku" label="SKU"   align="center"  />
          <el-table-column prop="fnsku" label="fnsku"  align="center"  />
        </el-table>
<!--        <pagination
            v-show="downloadBatchCodeTotal>0"
            :total="downloadBatchCodeTotal"
            v-model:page="queryParamsBatchCode.pageNum"
            v-model:limit="queryParamsBatchCode.pageSize"
            @pagination="downloadBatchCodeList"
        />-->
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="downloadBatchcodeHandle">下 载</el-button>
          <el-button @click="downloadBatchcodeOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- FNSKU下载 -->
    <el-dialog :title="downloadFnskuTitle" v-model="downloadFnskuOpen" width="800px" append-to-body>
        <el-table :data="downloadFnskuList" @selection-change="handleDownloadFnskuSelectionChange"  >
        <el-table-column type="selection" width="55" align="center"  />
        <el-table-column prop="sku" label="sku"  align="center" />
        <el-table-column prop="fnsku" label="fnsku"  align="center" />
        <el-table-column prop="fnskuRemarks" label="fnsku描述"   align="center"  />
        <el-table-column prop="qty" label="数量"   align="center"  />
        </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="downloadFnskuHandle">下 载</el-button>
          <el-button @click="downloadFnskuOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- mrp下载 -->
    <el-dialog :title="downloadMrpTitle" v-model="downloadMrpOpen" width="400px" append-to-body>
        <el-table :data="downloadMrpList" @selection-change="handleDownloadMrpSelectionChange"  >
        <el-table-column type="selection" width="55" align="center"  />
        <el-table-column prop="sku" label="sku"  align="center" />
        </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="downloadMrpHandle">下 载</el-button>
          <el-button @click="downloadMrpOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- wms码下载 -->
    <el-dialog :title="downloadWmsTitle" v-model="downloadWmsOpen" width="400px" append-to-body>
        <el-table :data="downloadWmsList" @selection-change="handleDownloadWmsSelectionChange"  >
        <el-table-column type="selection" width="55" align="center"  />
        <el-table-column prop="sku" label="sku"  align="center" />
        </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="downloadWmsHandle">下 载</el-button>
          <el-button @click="downloadWmsOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- UPC码下载 -->
    <el-dialog :title="downloadUpcTitle" v-model="downloadUpcOpen" width="800px" append-to-body>
      <el-table :data="downloadUpcList" @selection-change="handleDownloadUpcSelectionChange"  >
        <el-table-column type="selection" width="55" align="center"  />
        <el-table-column prop="item_mapping_id" label="id"  align="center" />
        <el-table-column prop="sku" label="sku"  align="center" />
        <el-table-column prop="upc" label="upc码"  align="center" />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="downloadUpcHandle">下 载</el-button>
          <el-button @click="downloadUpcOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="PurchaseOrderConfirm">
import {
  listPurchaseOrderConfirm,
  getPurchaseOrderPreview,
  getPurchaseOrderPdf,
  getPurchaseOrderReceipt,
  getPurchaseOrderBatchCode,
  getPurchaseOrderFnskuCode,
  getPurchaseOrderMrpCode,
  getPurchaseOrderWmsCode,
  getPurchaseOrderBatchCodeList,
  getPurchaseOrderFnskuList,
  getPurchaseOrderMrpList,
  getPurchaseOrderWmsList,
  getPurchaseOrderUpcList,
  getPurchaseOrderUpcCode
} from "@/api/order/purchaseOrderConfirm";
import companyIcon from '@/assets/logo/companyIcon.jpg';
import { getToken } from "@/utils/auth";
import {downloadJson} from "@/utils/request.js";

const { proxy } = getCurrentInstance();
const { tb_purchase_order_confirm_data_status,tb_purchase_order_payment_status,tb_purchase_order_status } = proxy.useDict('tb_purchase_order_confirm_data_status','tb_purchase_order_payment_status','tb_purchase_order_status');

// 初始化图片链接
const imageUrl = ref('');
const purchaseOrderInvoice = ref('');
const priceType = ref(0);
const purchaseOrderConfirmId = ref();
const purchaseOrderConfirmList = ref([]);
const downloadBatchCodeList = ref([]);
const downloadBatchCodeTotal = ref(0);
const downloadBatchcodeTitle = ref("");
const downloadBatchcodeOpen = ref(false);
//下载fnsku
const downloadFnskuList = ref([]);
const downloadFnskuTotal = ref(0);
const downloadFnskuTitle = ref("");
const downloadFnskuOpen = ref(false);
//下载Mrp
const downloadMrpList = ref([]);
const downloadMrpTotal = ref(0);
const downloadMrpTitle = ref("");
const downloadMrpOpen = ref(false);
//下载Wms
const downloadWmsList = ref([]);
const downloadWmsTotal = ref(0);
const downloadWmsTitle = ref("");
const downloadWmsOpen = ref(false);
//下载upc
const downloadUpcList = ref([]);
const downloadUpcTotal = ref(0);
const downloadUpcTitle = ref("");
const downloadUpcOpen = ref(false);

const queryParamsBatchCode = ref({
  pageNum: 1,
  pageSize: 10,
});

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const downloadPurchaseInvoice = ref("");
const daterangePurchaseOrderNoticeTime = ref([]);
const daterangeCounterSignatureTime = ref([]);
const daterangeCreateTime = ref([]);
const daterangeReviewTime = ref([]);
const activeName = ref('waitConfirm');

/*** 用户导入参数 */
const upload = reactive({
  uploadId: 0,
  // 是否显示弹出层
  open: false,
  // 弹出层标题
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/order/purchaseOrderConfirm/importData"
});

const previewData = ref({});
const purchaseOrderDetailList = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    purchaseInvoice: null,
    purchaseCounterSignatureUserName: null,
    dataStatus: null,
    sku: null,
    keywordCn: null,
    paymentStatus: null,
    purchaseOrderStatus: null,
    purchaseOrderNoticeTime: null,
    counterSignatureBy: null,
    counterSignatureTime: null,
    activeName: null,
    createById: null,
    createBy: null,
    createTime: null,
  }
});

// 选择的行数据
const selectedRows = ref([]);

// 多选框选中数据
function handleSelectionChange(selection) {
  selectedRows.value = selection;
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 批量下载回单 */
function handleDownload() {
  if (ids.value.length === 0) {
    proxy.$modal.msgError("请选择要下载的采购单");
    return;
  }
  downloadJson("order/purchaseOrderConfirm/receipt/downloads", ids.value, `采购单回单_${new Date().getTime()}.zip`);
}

const { queryParams } = toRefs(data);

// 选择的行数据
const selectedDownloadBatchCodeRows = ref([]);
const idsDownloadBatchCode = ref([]);
// 批次码多选框选中数据
function handleDownloadBatchCodeSelectionChange(selection) {
  selectedDownloadBatchCodeRows.value = selection;
  idsDownloadBatchCode.value = selection.map(item => item.id);
}

// 选择的行数据
const selectedDownloadFnskuRows = ref([]);
const idsDownloadFnsku = ref([]);
// fnsku多选框选中数据
function handleDownloadFnskuSelectionChange(selection) {
  idsDownloadFnsku.value = [];
  selectedDownloadFnskuRows.value = selection;
  if (selection.length > 0) {
    selection.forEach(item => {
    let purchaseOrderDetail = {};
    purchaseOrderDetail.fnsku = item.fnsku;
    purchaseOrderDetail.sku = item.sku;
    purchaseOrderDetail.qty = item.qty;
    purchaseOrderDetail.orderId = item.orderId;
    purchaseOrderDetail.fnskuRemarks = item.fnskuRemarks;
    idsDownloadFnsku.value.push(purchaseOrderDetail);
  });
  }else {
    idsDownloadFnsku.value = [];
  }
  console.log("idsDownloadFnsku:",idsDownloadFnsku)
}

// 选择的行数据
const selectedDownloadMrpRows = ref([]);
const idsDownloadMrp = ref([]);
// fnsku多选框选中数据
function handleDownloadMrpSelectionChange(selection) {
  selectedDownloadMrpRows.value = selection;
  idsDownloadMrp.value = [];
  if (selection.length > 0) {
    selection.forEach(item => {
    let purchaseOrderDetail = {};
    purchaseOrderDetail.sku = item.sku;
    purchaseOrderDetail.orderId = item.orderId;
    idsDownloadMrp.value.push(purchaseOrderDetail);
  });
  }else {
    idsDownloadMrp.value = [];
  }

  console.log("idsDownloadFnsku:",idsDownloadMrp)
}

// 选择的行数据
const selectedDownloadWmsRows = ref([]);
const idsDownloadWms = ref([]);

// Wms多选框选中数据
function handleDownloadWmsSelectionChange(selection) {
  selectedDownloadWmsRows.value = selection;
  idsDownloadWms.value = [];
  console.log("selection.length:",selection.length)
  if (selection.length > 0){
    selection.forEach(item => {
      let purchaseOrderDetail = {};
      purchaseOrderDetail.sku = item.sku;
      purchaseOrderDetail.orderId = item.orderId;
      purchaseOrderDetail.brandId = item.brandId;
      idsDownloadWms.value.push(purchaseOrderDetail);
    });
  }else {
    idsDownloadWms.value = [];
    }
  }

// 选择的行数据
const selectedDownloadUpcRows = ref([]);
const itemMappingIdList = ref(null);
// fnsku多选框选中数据
function handleDownloadUpcSelectionChange(selection) {
  selectedDownloadWmsRows.value = selection;
  // 收集所有选中的 item_mapping_id 到一个数组中
  let itemMappingIds = selection.map(item => item.item_mapping_id);
  console.log("itemMappingIds:",itemMappingIds)
  // 将 item_mapping_ids 数组转换为逗号分隔的字符串
  itemMappingIdList.value = itemMappingIds.join(',');
  console.log("itemMappingIdList:",itemMappingIdList)
}



/** 查询采购订单确认列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  proxy.addDateRange(queryParams.value, daterangePurchaseOrderNoticeTime.value,"PurchaseOrderNoticeTime")
  proxy.addDateRange(queryParams.value, daterangeCounterSignatureTime.value,"CounterSignatureTime")
  proxy.addDateRange(queryParams.value, daterangeCreateTime.value,"CreateTime")
  proxy.addDateRange(queryParams.value, daterangeReviewTime.value,"ReviewTime")
  queryParams.value.activeName = activeName.value;
  listPurchaseOrderConfirm(queryParams.value).then(response => {
    purchaseOrderConfirmList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "上传结果", { dangerouslyUseHTMLString: true });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};

/** 导入按钮操作 */
function handleImport(row) {
  const id = row.id ;
  console.log("回单上传")
  upload.title = "回单上传";
  upload.uploadId = id;
  upload.open = true;
};

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    vendorId: null,
    supplierId: null,
    purchaseOrderId: null,
    purchaseInvoice: null,
    invoice: null,
    purchaseCounterSignatureUserId: null,
    purchaseCounterSignatureUserName: null,
    dataStatus: null,
    purchaseOrderNoticeTime: null,
    counterSignatureBy: null,
    counterSignatureTime: null,
    createById: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    version: null,
    remark: null
  };
  proxy.resetForm("purchaseOrderConfirmRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function printPDF() {
  getPurchaseOrderPdf(purchaseOrderConfirmId.value).then(response => {
    console.log("下载采购单pdf:response",response);
    proxy.$modal.msgSuccess("下载成功");
    const blob = new Blob([response], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download =  "采购单" + purchaseOrderInvoice.value + '.pdf'; // 下载文件名
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);


  }).catch(error => {
    console.error('Error downloading PDF:', error);
  });
}

/** 下载回单 */
function downloadReceipt(row) {
  const id = row.id ;
  getPurchaseOrderReceipt(id).then(response => {
  const url = response.data.generatorOuterLink || '';
  console.log("下载回单url", url);

  if (url) {
    // 使用 window.open 在新标签页中打开下载链接
    window.open(url, '_blank');
  } else {
    console.error('没有可用的下载链接');
  }
  }).catch(error => {
    console.error('获取下载链接时出错:', error);
  });
};



/** 下载批次码 */
function downloadBatchCode(row) {
    //打开页面时清空idsDownloadBatchCode
    idsDownloadBatchCode.value = [];
    const id = row.id ;
    downloadBatchcodeOpen.value = true;
    downloadBatchcodeTitle.value = "批次码下载";
    downloadPurchaseInvoice.value = row.purchaseInvoice;
    queryParamsBatchCode.value.id = id;
    getPurchaseOrderBatchCodeList(id).then(response => {
    downloadBatchCodeList.value = response.rows;
      batchCodeTotal.value = response.total;
  });
};

/** FNSKU条码列表 */
function downloadFnsku(row) {
  //打开页面时清空idsDownloadFnsku
  idsDownloadFnsku.value = [];
  const id = row.id ;
  downloadFnskuOpen.value = true;
  downloadFnskuTitle.value = "FNSKU条码下载";
  downloadPurchaseInvoice.value = row.purchaseInvoice;
  getPurchaseOrderFnskuList(id).then(response => {
  downloadFnskuList.value = response.rows;
  downloadFnskuTotal.value = response.total;
  });
}

/** Mrp条码列表 */
function downloadMrp(row) {
  //打开页面时清空idsDownloadMrp
  idsDownloadMrp.value = [];
  const id = row.id ;
  downloadMrpOpen.value = true;
  downloadMrpTitle.value = "Mrp条码下载";
  downloadPurchaseInvoice.value = row.purchaseInvoice;
  getPurchaseOrderMrpList(id).then(response => {
  downloadMrpList.value = response.rows;
  downloadMrpTotal.value = response.total;
  });
}

/** Wms条码列表 */
function downloadWms(row) {
  //打开页面时清空idsDownloadWms
  idsDownloadWms.value = [];
  const id = row.id ;
  downloadWmsOpen.value = true;
  downloadWmsTitle.value = "wms条码下载";
  downloadPurchaseInvoice.value = row.purchaseInvoice;
  getPurchaseOrderWmsList(id).then(response => {
  downloadWmsList.value = response.rows;
  downloadWmsTotal.value = response.total;
  });
}

/** Wms条码列表 */
function downloadUpc(row) {
  //打开页面时清空idsDownloadWms
  itemMappingIdList.value = [];
  const id = row.id ;
  downloadUpcOpen.value = true;
  downloadUpcTitle.value = "wms条码下载";
  downloadPurchaseInvoice.value = row.purchaseInvoice;
  getPurchaseOrderUpcList(id).then(response => {
  downloadUpcList.value = response.rows;
  downloadUpcTotal.value = response.total;
  });
}

/** 下载批次码 */
function downloadBatchcodeHandle(row) {
  let ids = idsDownloadBatchCode.value;
  if (ids.length == 0){
    proxy.$modal.msgError("请选择要下载的批次码");
    return;
  }
  downloadJson("order/purchaseOrderConfirm/batchCode/download", ids , `批次码${new Date().getTime()}.zip`);
};



/** 下载FNSKU条码 */
function downloadFnskuHandle(row) {
  let details = idsDownloadFnsku.value;
  console.log("idsDownloadFnsku",details)
  if (details.length == 0){
    proxy.$modal.msgError("请选择要下载的fnsku条码");
    return;
  }
  downloadJson("order/purchaseOrderConfirm/fnskuCode/download", details , `fnsku条码${new Date().getTime()}.zip`);
};

/** 下载MRP条码 */
function downloadMrpHandle(row) {
  let details = idsDownloadMrp.value;
  console.log("idsDownloadMrp",details)
  if (details.length == 0){
    proxy.$modal.msgError("请选择要下载的mrp条码");
    return;
  }
  let failString = ".zip";
  downloadJson("order/purchaseOrderConfirm/mrpCode/download", details , `Mrp条码${new Date().getTime()}` + failString);
};

/** 下载WMS条码 */
function downloadWmsHandle(row) {
  let details = idsDownloadWms.value;
  console.log("idsDownloadWms",details)
  if (details.length == 0){
    proxy.$modal.msgError("请选择要下载的Wms条码");
    return;
  }
  downloadJson("order/purchaseOrderConfirm/wmsCode/download", details , `WMS条码${new Date().getTime()}.zip`);
};

/** 下载upc条码 */
function downloadUpcHandle(row) {
  if (itemMappingIdList.value == null || itemMappingIdList.value == ""){
    proxy.$modal.msgError("请选择要下载的upc条码");
    return;
  }
  proxy.download("order/purchaseOrderConfirm/upcCode/download?itemMappingIdList=" + itemMappingIdList.value, {
  }, `upc文件下载${new Date().getTime()}.zip`);
};

/** 预览 */
function handlePreview(row) {
  const id = row.id ;
  getPurchaseOrderPreview(id).then(response => {
    previewData.value = response.data;
    purchaseOrderDetailList.value = response?.data.purchaseOrderDetailList;
    imageUrl.value = response.data.ossUploadFileLink;
    priceType.value = response.data.purchaseOrder.priceType;
    purchaseOrderInvoice.value = response.data.purchaseOrder.invoice;
    purchaseOrderConfirmId.value = id;
    console.log("previewData",previewData.value);
    console.log("priceType",priceType.value);
    open.value = true;
    title.value = "采购单预览";
  });
};

/** 重置按钮操作 */
function resetQuery() {
  daterangePurchaseOrderNoticeTime.value = [];
  daterangeCounterSignatureTime.value = [];
  daterangeCreateTime.value = [];
  daterangeReviewTime.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

const handleTabClick = (tab) => {
  activeName.value = tab.props.name;
  getList();
};

getList();
</script>
