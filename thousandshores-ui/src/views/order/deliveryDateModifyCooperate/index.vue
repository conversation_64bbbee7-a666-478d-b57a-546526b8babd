<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="采购单号" prop="purchaseInvoice">
        <el-input
          style="width: 240px"
          v-model="queryParams.purchaseInvoice"
          placeholder="请输入采购单号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="详情id" prop="purchaseOrderDetailId">
        <el-input
            style="width: 240px"
          v-model="queryParams.purchaseOrderDetailId"
          placeholder="请输入详情id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="sku" prop="sku">
        <el-input
            style="width: 240px"
          v-model="queryParams.sku"
          placeholder="请输入sku"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="中文关键字" prop="keywordCn">
        <el-input
            style="width: 240px"
          v-model="queryParams.keywordCn"
          placeholder="请输入中文关键字"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="fnsku" prop="fnsku">
        <el-input
            style="width: 240px"
          v-model="queryParams.fnsku"
          placeholder="请输入fnsku"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入库仓" prop="purchaseWarehouseName">
        <el-input
            style="width: 240px"
          v-model="queryParams.purchaseWarehouseName"
          placeholder="请输入入库仓"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="行状态" prop="lineStatus">
        <el-select style="width: 240px" v-model="queryParams.lineStatus" placeholder="请选择行状态" clearable>
          <el-option
              v-for="dict in purchase_order_delivery_modify_line_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="采购明细类型" prop="detailType">
        <el-select style="width: 240px" v-model="queryParams.detailType" placeholder="请选择采购单明细类型" clearable>
          <el-option
            v-for="dict in purchase_order_detail_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="质检类型" prop="detailQcType">
        <el-select style="width: 240px" v-model="queryParams.detailQcType" placeholder="请选择质检类型" clearable>
          <el-option
            v-for="dict in qc_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="最新交期" style="width: 340px"  >
        <el-date-picker
          v-model="daterangeFinalDeliveryDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="入库日期" style="width: 340px" >
        <el-date-picker
            clearable
            v-model="daterangeWarehousingDate"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="超期标识" prop="overTimeStatus">
        <el-select style="width: 240px" v-model="queryParams.overTimeStatus" placeholder="请选择超期标识" clearable>
          <el-option
              v-for="dict in purchase_order_delivery_modify_overtime_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="国家代号" prop="purchaseWarehouseName">
        <el-input
            style="width: 240px"
          v-model="queryParams.countrySymbol"
          placeholder="请输入国家代号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="multiple"
          @click="handleUpdate"
          v-hasPermi="['order:purchaseOrderDeliveryDateModifyApplyCooperate:edit']"
        >交期变更</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Upload"
            @click="handleImport()"
            v-hasPermi="['order:purchaseOrderDeliveryDateModifyApplyCooperate:edit']"
        >导入</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['order:purchaseOrderDeliveryDateModifyApplyCooperate:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>


    <el-table v-loading="loading" :data="purchaseOrderDeliveryDateModifyCooperateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" :selectable="isRowSelectable"  width="55" align="center" fixed="left" />
      <el-table-column label="详情id" align="center" prop="purchaseOrderDetailId" fixed="left" />
      <el-table-column label="采购单号" width="120" align="center" prop="purchaseInvoice" fixed="left" />
      <el-table-column label="sku" width="120" align="center" prop="sku" fixed="left" />
      <el-table-column label="行号" align="center" :show-overflow-tooltip="true" prop="serialNumber" />
      <el-table-column label="行状态" align="center" prop="lineStatus">
        <template #default="scope">
          <dict-tag :options="purchase_order_delivery_modify_line_status" :value="scope.row.lineStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="中文关键字" width="200" align="center" :show-overflow-tooltip="true" prop="keywordCn" />
      <el-table-column label="fnsku" width="120" align="center" prop="fnsku" />
      <el-table-column label="入库仓" width="120" align="center" prop="purchaseWarehouseName" />
      <el-table-column label="国家代号" width="120" align="center" prop="countrySymbol" />
      <el-table-column label="采购类型" align="center" prop="detailType">
        <template #default="scope">
          <dict-tag :options="purchase_order_detail_type" :value="scope.row.detailType"/>
        </template>
      </el-table-column>
      <el-table-column label="质检类型" align="center" prop="detailQcType">
        <template #default="scope">
          <dict-tag :options="qc_type" :value="scope.row.detailQcType"/>
        </template>
      </el-table-column>
      <el-table-column label="单位" align="center" prop="unit" />
      <el-table-column label="采购数量" align="center" prop="purchaseQty" />
      <el-table-column label="已收数量" align="center" prop="alreadyReceiveQty" />
      <el-table-column label="未收数量" align="center" prop="notReceiveQty" />
      <el-table-column label="入库数量" align="center" prop="alreadyConfirmedQty" />
      <el-table-column label="初始交期" align="center" prop="initialDeliveryDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.initialDeliveryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最新交期" align="center" prop="finalDeliveryDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.finalDeliveryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入库日期" align="center" prop="warehousingDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.warehousingDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="超期标识" align="center" prop="overTimeStatus" />
      <el-table-column label="出厂价" align="center">
        <template #default="scope">
          {{ scope.row.priceCheck === 'YES' ? (scope.row.purchasePrice - scope.row.warehouseInFee).toFixed(2)  : '###'}}
        </template>
      </el-table-column>
      <el-table-column label="运费" align="center">
        <template #default="scope">
          {{ scope.row.priceCheck === 'YES' ? scope.row.warehouseInFee.toFixed(2) : '###' }}
        </template>
      </el-table-column>
      <el-table-column label="总单价" align="center">
        <template #default="scope">
          {{ scope.row.priceCheck === 'YES' ? scope.row.purchasePrice.toFixed(2) : '###' }}
        </template>
      </el-table-column>
      <el-table-column label="总价" align="center">
        <template #default="scope">
          {{ scope.row.priceCheck === 'YES' ? scope.row.total.toFixed(2) : '###' }}
        </template>
      </el-table-column>
      <el-table-column label="装箱数" align="center" prop="packingQuantity" />
      <el-table-column width="200"  label="修改原因"  align="center" prop="updateChildItem">
        <template #default="scope">
          <el-select  v-model="scope.row.updateChildItem" placeholder="请选择修改原因" >
            <el-option
                v-for="dict in purchase_order_update_reason_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column width="200"  label="变更备注"  align="center" prop="updateRemark">
        <template #default="scope">
          <el-input
              style="width: 180px"
              v-model="scope.row.updateRemark"
              placeholder="请输入备注"
              maxlength="50"
              show-word-limit
          />
        </template>
      </el-table-column>
      <el-table-column width="200" label="交期1" align="center">
        <template #default="scope">
          <el-date-picker
                style="width: 150px"
                v-model="scope.row.deliveryDate1"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="交期1">
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column width="120" label="数量1" align="center">
        <template #default="scope">
          <el-input
              style="width: 90px"
              type="number"
              v-model="scope.row.qty1"
              placeholder="数量1"
          />
        </template>
      </el-table-column>
      <el-table-column width="200" label="交期2" align="center">
        <template #default="scope">
          <el-date-picker
              style="width: 150px"
              v-model="scope.row.deliveryDate2"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="交期2">
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column width="120" label="数量2" align="center">
        <template #default="scope">
          <el-input
              style="width: 90px"
              type="number"
              v-model="scope.row.qty2"
              placeholder="数量2"
          />
        </template>
      </el-table-column>
      <el-table-column width="200" label="交期3" align="center">
        <template #default="scope">
          <el-date-picker
              style="width: 150px"
              v-model="scope.row.deliveryDate3"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="交期3">
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column width="120" label="数量3" align="center">
        <template #default="scope">
          <el-input
              style="width: 90px"
              type="number"
              v-model="scope.row.qty3"
              placeholder="数量3"
          />
        </template>
      </el-table-column>
      <el-table-column width="200" label="交期4" align="center">
        <template #default="scope">
          <el-date-picker
              style="width: 150px"
              v-model="scope.row.deliveryDate4"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="交期4">
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column width="120" label="数量4" align="center">
        <template #default="scope">
          <el-input
              style="width: 90px"
              type="number"
              v-model="scope.row.qty4"
              placeholder="数量4"
          />
        </template>
      </el-table-column>
      <el-table-column width="200" label="交期5" align="center">
        <template #default="scope">
          <el-date-picker
              style="width: 150px"
              v-model="scope.row.deliveryDate5"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="交期5">
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column width="120" label="数量5" align="center">
        <template #default="scope">
          <el-input
              style="width: 90px"
              type="number"
              v-model="scope.row.qty5"
              placeholder="数量5"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />


    <!-- 交期变更上传 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="70%" append-to-body>
      <el-upload
          ref="uploadRef"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em> 仅允许导入xls、xlsx格式文件，请先导出数据后再导入。</div>
      </el-upload>
      <div style="text-align: center" >
        <el-button type="primary" @click="submitFileForm()">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
      <div style="margin-top: 5px" >

      </div>
      <el-table :data="uploadFileLogList" v-loading="fileUploadLogLoading"   style="width: 100%;">
        <el-table-column prop="id" label="id" width="60" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center" >
          <template #default="scope">
            <dict-tag :options="sys_file_upload_log_status" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="上传时间" width="200" align="center" >
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="上传人" align="center" />
        <el-table-column prop="fileName" label="文件名" width="300" align="center"  />
        <el-table-column prop="total" label="总条数"  align="center" />
        <el-table-column prop="success" label="成功数" align="center" />
        <el-table-column label="上传进度" align="center" >
          <template #default="scope">
            <el-progress :text-inside="true" :stroke-width="15" :percentage="parseInt(scope.row.uploadProgress)"  />
          </template>
        </el-table-column>
        <el-table-column label="错误数" align="center" >
          <template #default="scope">
            <a href="#" class="clickable" @click.prevent="handleClick(scope.row)">
              {{ scope.row.error }}
            </a>
          </template>
        </el-table-column>
      </el-table>
      <pagination
          v-show="fileUploadLogTotal>0"
          :total="fileUploadLogTotal"
          v-model:page="fileUploadLogQueryParams.pageNum"
          v-model:limit="fileUploadLogQueryParams.pageSize"
          @pagination="getFileUploadLogList"
      />

    </el-dialog>

    <!-- 上传错误日志 -->
    <el-dialog :title="uploadError.title" v-model="uploadError.open" width="70%" append-to-body>

      <el-table :data="uploadFileErrorLogList" style="width: 100%"  >
        <el-table-column prop="id" label="id" width="100"  align="center" />
        <el-table-column prop="line" label="excel行号" width="100" align="center" />
        <el-table-column prop="content" label="错误内容"  align="center"  />
      </el-table>
      <pagination
          v-show="fileUploadErrorLogTotal>0"
          :hide-on-single-page="true"
          :total="fileUploadErrorLogTotal"
          v-model:page="fileUploadErrorLogQueryParams.pageNum"
          v-model:limit="fileUploadErrorLogQueryParams.pageSize"
          @pagination="getFileUploadErrorLogList"

      />
      <!-- <div class="dialog-footer"  >
        <el-button  @click="uploadError.open = false" >关 闭</el-button>
      </div>-->
    </el-dialog>
  </div>



</template>

<style scoped>
.clickable {
  cursor: pointer;
  color: blue;
}
</style>

<script setup name="PurchaseOrderDeliveryDateModifyApplyCooperate">
import { listPurchaseOrderDeliveryDateModifyApplyCooperate,updatePurchaseOrderDeliveryDateModifyApplyCooperate } from "@/api/order/purchaseOrderDeliveryDateModifyApplyCooperate";
import { getToken } from "@/utils/auth";
import {listFileUploadErrorLog, listFileUploadLog} from "@/api/system/fileUploadLog.js";

const { proxy } = getCurrentInstance();
const { purchase_order_delivery_modify_data_status, qc_type, purchase_order_delivery_modify_confirm_status, purchase_order_detail_type,
  purchase_order_delivery_modify_overtime_type, purchase_order_update_reason_type, purchase_order_delivery_modify_line_status,
  sys_file_upload_log_status }
    = proxy.useDict('purchase_order_delivery_modify_data_status', 'qc_type', 'purchase_order_delivery_modify_confirm_status',
    'purchase_order_detail_type', 'purchase_order_delivery_modify_overtime_type', 'purchase_order_update_reason_type', 'purchase_order_delivery_modify_line_status',
    'sys_file_upload_log_status');

const purchaseOrderDeliveryDateModifyCooperateList = ref([]);
const uploadFileLogList = ref([]);
const uploadFileErrorLogList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const fileUploadErrorLogId = ref(0);

const fileUploadLogTotal = ref(0);
const fileUploadLogLoading = ref(true);
const fileUploadLogQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  action: 'purchase_order_delivery_date_update'
});

const fileUploadErrorLogTotal = ref(0);
const fileUploadErrorLogQueryParams = ref({
  fileUploadLogId:null,
  pageNum: 1,
  pageSize: 10
});


// 模拟上传任务
const simulateUploadTask = () => {
  console.log("定时任务执行");
  let taskId = setInterval(() => {
    // 更新数据源
    updateUploadProgress();
    // 当所有上传完成时停止定时器
    if (uploadFileLogList.value.every(item => item.uploadProgress === 100 )) {
      clearInterval(taskId);
    }
  }, 1000); // 每秒更新一次进度

  return taskId;
};

// 更新上传进度
const updateUploadProgress = () => {
  uploadFileLogList.value.forEach(item => {
    if (item.uploadProgress < 100 && item.status === 1) {
      item.uploadProgress += Math.random() * 10; // 随机增加进度
      item.uploadProgress = Math.min(item.uploadProgress, 100); // 确保不超过100%
    }
  });
};

const title = ref("");
const daterangeInitialDeliveryDate = ref([]);
const daterangeFinalDeliveryDate = ref([]);
const daterangeWarehousingDate = ref([]);
const isRowSelectable = computed(() => {
  return (row) => row.lineStatus !== 'already_in_stock' && row.lineStatus !== 'cancel'  && row.lineStatus !== 'fully_received' ;
});

/*** 上传错误日志 */
const uploadError = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题
  title: ""
});

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/order/purchaseOrderDeliveryDateModifyApplyCooperate/upload"
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    purchaseInvoice: null,
    purchaseOrderDetailId: null,
    sku: null,
    keywordCn: null,
    fnsku: null,
    purchaseWarehouseName: null,
    countrySymbol: null,
    lineStatus: null,
    detailType: null,
    detailQcType: null,
    initialDeliveryDate: null,
    updateChildItem: null,
    modifyStatus: null,
    overTimeStatus:null
  }
});

const { queryParams} = toRefs(data);


/** 查询列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  proxy.addDateRange(queryParams.value, daterangeFinalDeliveryDate.value,"FinalDeliveryDate")
  proxy.addDateRange(queryParams.value, daterangeWarehousingDate.value,"WarehousingDate")
  listPurchaseOrderDeliveryDateModifyApplyCooperate(queryParams.value).then(response => {
    if (response.rows){

    }
    purchaseOrderDeliveryDateModifyCooperateList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询上传日志 */
function getFileUploadLogList() {
  fileUploadLogLoading.value = true;
  console.log("查询上传日志", fileUploadLogQueryParams.value);
  listFileUploadLog(fileUploadLogQueryParams.value).then(response => {
    uploadFileLogList.value = response.rows;
    fileUploadLogTotal.value = response.total;
    fileUploadLogLoading.value = false;
  });
  //监听进度
  simulateUploadTask();
}

/** 查询上传错误日志 */
function getFileUploadErrorLogList() {
  fileUploadLogQueryParams.fileUploadLogId = fileUploadErrorLogId;
  console.log("查询上传错误日志", fileUploadErrorLogQueryParams.value);
  listFileUploadErrorLog(fileUploadErrorLogQueryParams.value).then(response => {
    uploadFileErrorLogList.value = response.rows;
    fileUploadErrorLogTotal.value = response.total;
  });

}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 上传错误日志数据 */
function handleClick(row) {
  fileUploadErrorLogId.value = row.id ;
  fileUploadErrorLogQueryParams.value.fileUploadLogId = row.id;
  listFileUploadErrorLog(fileUploadErrorLogQueryParams.value).then(response => {
    uploadFileErrorLogList.value = response.rows;
    fileUploadErrorLogTotal.value = response.total;
    uploadError.open = true;
    uploadError.title = "错误日志";
  });
};

/** 重置按钮操作 */
function resetQuery() {
  daterangeFinalDeliveryDate.value = [];
  daterangeInitialDeliveryDate.value = [];
  daterangeWarehousingDate.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 选择的行数据
const selectedRows = ref([]);

// 多选框选中数据
function handleSelectionChange(selection) {
  selectedRows.value = selection;
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "上传结果", { dangerouslyUseHTMLString: true });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
};

/** 导入按钮操作 */
function handleImport() {
  fileUploadLogLoading.value = true;
  listFileUploadLog(fileUploadLogQueryParams.value).then(response => {
    uploadFileLogList.value = response.rows;
    fileUploadLogTotal.value = response.total;
    console.log("交期变更上传")
    upload.title = "交期变更上传";
    upload.open = true;
    fileUploadLogLoading.value = false;
  });
  //监听进度
  simulateUploadTask();
};

/** 交期变更按钮操作 */
function handleUpdate(row) {
  const _id = row.id || ids.value;
  let warning = "";
  selectedRows.value.forEach(item => {
    warning += "详情id：" +item.purchaseOrderDetailId + ",";
    if (item.qty1 != null && item.packingQuantity != null && item.qty1 % item.packingQuantity != 0) {
      warning += "对应数量" + item.qty1 + "不是整箱数，";
    }
    if (item.qty2 != null && item.packingQuantity != null && item.qty2 % item.packingQuantity != 0) {
      warning += "对应数量" + item.qty2 + "不是整箱数，";
    }
    if (item.qty3 != null && item.packingQuantity != null && item.qty3 % item.packingQuantity != 0) {
      warning += "对应数量" + item.qty3 + "不是整箱数，";
    }
    if (item.qty4 != null && item.packingQuantity != null && item.qty4 % item.packingQuantity != 0) {
      warning += "对应数量" + item.qty4 + "不是整箱数";
    }
    if (item.qty5 != null && item.packingQuantity != null && item.qty5 % item.packingQuantity != 0) {
      warning += "对应数量" + item.qty5 + "不是整箱数";
    }
  })
  warning += " 确认变更交期？";
  proxy.$modal.confirm(warning).then(function() {
    return updatePurchaseOrderDeliveryDateModifyApplyCooperate(selectedRows.value);
  }).then(() => {
    proxy.$modal.msgSuccess("修改成功");
    getList();
  })
}

function getValidType(type) {
  const validTypes = ['primary', 'success', 'info', 'warning', 'danger'];
  return validTypes.includes(type) ? type : 'info'; // 默认类型为 'info'
}

onMounted(() => {
  getValidType();
});

/** 导出按钮操作 */
function handleExport() {
  proxy.download('order/purchaseOrderDeliveryDateModifyApplyCooperate/export', {
    ...queryParams.value
  }, `交期变更${new Date().getTime()}.xlsx`)
}

  getList();
</script>
