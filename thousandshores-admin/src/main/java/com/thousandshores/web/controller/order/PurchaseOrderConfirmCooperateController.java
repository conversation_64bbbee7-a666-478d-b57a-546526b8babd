package com.thousandshores.web.controller.order;

import java.util.Collections;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.thousandshores.common.annotation.Log;
import com.thousandshores.common.core.controller.BaseController;
import com.thousandshores.common.core.domain.AjaxResult;
import com.thousandshores.common.core.domain.model.LoginUser;
import com.thousandshores.common.core.page.TableDataInfo;
import com.thousandshores.common.core.redis.RedisKey;
import com.thousandshores.common.enums.BusinessType;
import com.thousandshores.common.enums.NoticeMessageMenuEnum;
import com.thousandshores.common.exception.ServiceException;
import com.thousandshores.common.utils.RedisUtils;
import com.thousandshores.common.utils.poi.ExcelUtil;
import com.thousandshores.domain.order.PurchaseOrderConfirmCooperate;
import com.thousandshores.service.order.IPurchaseOrderConfirmCooperateService;
import com.thousandshores.system.domain.erpentity.PurchaseOrderDetail;
import com.thousandshores.system.service.ISysNoticeMessageService;
import com.thousandshores.system.service.erp.PurchaseOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 采购订单确认Controller
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/order/purchaseOrderConfirm")
public class PurchaseOrderConfirmCooperateController extends BaseController
{
    private final Logger logs = LoggerFactory.getLogger( this.getClass() );

    @Autowired
    private IPurchaseOrderConfirmCooperateService purchaseOrderConfirmCooperateService;

    @Autowired
    private PurchaseOrderService purchaseOrderService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private ISysNoticeMessageService sysNoticeMessageService;

    /**
     * 查询采购订单确认列表
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:list')")
    @GetMapping("/list")
    @Log(title = "订单确认列表查询", businessType = BusinessType.QUERY)
    public TableDataInfo list(PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate)
    {
        startPage();
        purchaseOrderConfirmCooperate.setPriceCheck( getLoginUser().getUser().getPriceCheck() );
        List<PurchaseOrderConfirmCooperate> list = purchaseOrderConfirmCooperateService.selectPurchaseOrderConfirmCooperateList(purchaseOrderConfirmCooperate);
        return getDataTable(list);
    }

    /**
     * 导出采购订单确认列表
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:export')")
    @Log(title = "采购订单确认", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate)
    {
        purchaseOrderConfirmCooperate.setPriceCheck( getLoginUser().getUser().getPriceCheck() );
        List<PurchaseOrderConfirmCooperate> list = purchaseOrderConfirmCooperateService.selectPurchaseOrderConfirmCooperateList(purchaseOrderConfirmCooperate);
        ExcelUtil<PurchaseOrderConfirmCooperate> util = new ExcelUtil<PurchaseOrderConfirmCooperate>(PurchaseOrderConfirmCooperate.class);
        util.exportExcel(response, list, "采购订单确认数据");
    }

    /**
     * 获取采购订单确认详细信息
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @GetMapping(value = "/{id}")
    @Log(title = "订单确认查询", businessType = BusinessType.QUERY)
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(purchaseOrderConfirmCooperateService.selectPurchaseOrderConfirmCooperateById(id));
    }

    /**
     * 采购订单信息预览
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @GetMapping(value = "/preview/{id}")
    @Log(title = "订单确认预览", businessType = BusinessType.QUERY)
    public AjaxResult preview(@PathVariable("id") Long id)
    {
        LoginUser loginUser = getLoginUser();
        return success(purchaseOrderConfirmCooperateService.selectPurchaseOrderPreviewById(id,loginUser));
    }

    /**
     * 采购订单信息PDF下载
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @GetMapping(value = "/pdf/{id}")
    @Log(title = "订单确认pdf下载", businessType = BusinessType.EXPORT)
    public void pdf(HttpServletResponse response,@PathVariable("id") Long id)
    {
        LoginUser loginUser = getLoginUser();
        purchaseOrderConfirmCooperateService.exportPdf(id,loginUser,response);
    }

    /**
     * 获取采购订单信息批次码集合
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @GetMapping(value = "/batchCodeList/{id}")
    @Log(title = "订单确认批次码分页查询", businessType = BusinessType.EXPORT)
    public TableDataInfo batchCodeList( @PathVariable("id") Long id)
    {
        LoginUser loginUser = getLoginUser();
        PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateService.selectPurchaseOrderConfirmCooperateById( id );
        //startPage();
        return getDataTable(purchaseOrderConfirmCooperateService.selectPurchaseBatchCodeList(purchaseOrderConfirmCooperate.getPurchaseInvoice(),loginUser));
    }

    /**
     * 采购订单信息批次码下载
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @PostMapping(value = "/batchCode/download")
    @Log(title = "订单确认批次码下载", businessType = BusinessType.EXPORT)
    public void batchCode(HttpServletResponse response, HttpServletRequest request,@RequestBody List<Long> ids)
    {
        LoginUser loginUser = getLoginUser();
        purchaseOrderConfirmCooperateService.exportBatchCode(ids,loginUser,response,request);
    }

    /**
     * 获取采购订单信息fnsku集合
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @GetMapping(value = "/fnskuList/{id}")
    @Log(title = "订单确认fnsku分页查询", businessType = BusinessType.EXPORT)
    public TableDataInfo fnskuList( @PathVariable("id") Long id)
    {
        LoginUser loginUser = getLoginUser();
        PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateService.selectPurchaseOrderConfirmCooperateById( id );
        //startPage();
        return getDataTable(purchaseOrderConfirmCooperateService.selectPurchaseFnskuList(purchaseOrderConfirmCooperate.getPurchaseOrderId(),loginUser));
    }

    /**
     * 采购订单信息FNSKU条码下载
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @PostMapping(value = "/fnskuCode/download")
    @Log(title = "订单确认fnsku下载", businessType = BusinessType.EXPORT)
    public void fnskuCode(HttpServletResponse response, HttpServletRequest request, @RequestBody List<PurchaseOrderDetail> detailList)
    {
        LoginUser loginUser = getLoginUser();
         purchaseOrderConfirmCooperateService.exportFnskuCode(detailList,loginUser,response,request);
    }

    /**
     * 获取采购订单信息mrp集合
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @GetMapping(value = "/mrpList/{id}")
    @Log(title = "订单确认mrp分页查询", businessType = BusinessType.EXPORT)
    public TableDataInfo mrpList( @PathVariable("id") Long id)
    {
        LoginUser loginUser = getLoginUser();
        PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateService.selectPurchaseOrderInfoByConfirmId( id );
        //startPage();
        return getDataTable(purchaseOrderConfirmCooperateService.selectPurchaseMrpList(purchaseOrderConfirmCooperate,loginUser));
    }

    /**
     * 采购订单信息Mrp条码下载
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @PostMapping(value = "/mrpCode/download")
    @Log(title = "订单确认mrp下载", businessType = BusinessType.EXPORT)
    public void mrpCode(HttpServletResponse response, HttpServletRequest request,@RequestBody List<PurchaseOrderDetail> detailList)
    {
        LoginUser loginUser = getLoginUser();
        try {
            purchaseOrderConfirmCooperateService.exportMrpCode(detailList,loginUser,response,request);
        }catch (Exception e){
            logs.error( "Mrp条码下载异常",e );
        }
    }

    /**
     * 获取采购订单信息wms集合
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @GetMapping(value = "/wmsList/{id}")
    @Log(title = "订单确认wms分页查询", businessType = BusinessType.EXPORT)
    public TableDataInfo wmsList( @PathVariable("id") Long id)
    {
        LoginUser loginUser = getLoginUser();
        PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateService.selectPurchaseOrderInfoByConfirmId( id );
        //startPage();
        return getDataTable(purchaseOrderConfirmCooperateService.selectPurchaseWmsList(purchaseOrderConfirmCooperate.getId(),loginUser));
    }

    /**
     * 获取采购订单信息wms集合
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @GetMapping(value = "/upcList/{id}")
    @Log(title = "订单确认upc分页查询", businessType = BusinessType.EXPORT)
    public TableDataInfo upcList( @PathVariable("id") Long id)
    {
        LoginUser loginUser = getLoginUser();
        PurchaseOrderConfirmCooperate purchaseOrderConfirmCooperate = purchaseOrderConfirmCooperateService.selectPurchaseOrderInfoByConfirmId( id );
        //startPage();
        return getDataTable(purchaseOrderService.selectPurchaseOrderUpcSkuList(purchaseOrderConfirmCooperate.getPurchaseOrderId()));
    }

    /**
     * 采购订单信息upc条码下载
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @PostMapping(value = "/upcCode/download")
    @Log(title = "订单确认upc条码下载", businessType = BusinessType.EXPORT)
    public void upcCode(HttpServletResponse response, String itemMappingIdList)
    {
        purchaseOrderConfirmCooperateService.exportUpcCode(itemMappingIdList,response);
    }

    /**
     * 采购订单信息WMS条码下载
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @PostMapping(value = "/wmsCode/download")
    @Log(title = "订单确认wms条码下载", businessType = BusinessType.EXPORT)
    public void wmsCode(HttpServletResponse response, HttpServletRequest request, @RequestBody List<PurchaseOrderDetail> detailList)
    {
        LoginUser loginUser = getLoginUser();
        purchaseOrderConfirmCooperateService.exportWmsCode(detailList,loginUser,response,request);
    }


    /**
     * 回单信息PDF下载
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @GetMapping(value = "/receipt/{id}")
    @Log(title = "订单确认回单下载", businessType = BusinessType.EXPORT)
    public AjaxResult receipt(HttpServletResponse response,@PathVariable("id") Long id)
    {
        return success(purchaseOrderConfirmCooperateService.exportReceipt(id,response));
    }

    /**
     * 回单批量下载
     */
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:query')")
    @PostMapping(value = "/receipt/downloads")
    @Log(title = "回单批量下载", businessType = BusinessType.EXPORT)
    public void receiptDownloads(HttpServletResponse response, HttpServletRequest request, @RequestBody List<Long> ids)
    {
        LoginUser loginUser = getLoginUser();
        purchaseOrderConfirmCooperateService.exportReceiptDownloads(ids,loginUser,response,request);
    }

    @Log(title = "回单上传", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('order:purchaseOrderConfirm:edit')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,Long id) throws Exception
    {
        LoginUser loginUser = getLoginUser();
        AjaxResult ajaxResult = AjaxResult.success();
        String key = RedisKey.PURCHASE_ORDER_CONFIRM_COOPERATE_UPLOAD + id;
        try {
            // 加锁
            redisUtils.lock( key,id.toString(),"回单上传中，请勿重复上传",120 );
            purchaseOrderConfirmCooperateService.importReceiptData( file, id,loginUser );
            //通知消息确认
            sysNoticeMessageService.confirmedNoticeMessage(Collections.singletonList( id ), NoticeMessageMenuEnum.订单确认 );
        }catch (ServiceException e){
            return handleException(e, id, "上传回单",null, Collections.singletonList( key ) );
        }
        redisUtils.unlock( key );
        return ajaxResult;
    }

}
